{"name": "@shape-construction/utils", "version": "1.0.0", "description": "Shared utilities for our apps", "engines": {"node": "v20.12.2", "pnpm": "9.0.4"}, "scripts": {"compile": "tsc -p tsconfig.json", "format": "biome format --write", "test": "jest", "test:ci": "jest --ci --coverage"}, "keywords": [], "author": "", "license": "ISC", "devDependencies": {"@biomejs/biome": "1.9.3", "typescript": "5.8.3"}, "dependencies": {"dayjs": "^1.11.5", "dompurify": "3.2.6", "html-react-parser": "3.0.13", "jest": "29.4.3", "ts-jest": "^29.4.0"}}