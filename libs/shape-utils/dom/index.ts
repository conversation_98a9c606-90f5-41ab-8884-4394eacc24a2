import DomPurify, { type Config as DomPurifyConfig } from 'dompurify';
import HTMLReactParser from 'html-react-parser';

export const sanitizeHTML = DomPurify.sanitize;

const defaultSanitizerOptions: DomPurifyConfig = {
  FORBID_ATTR: ['style'],
};

export const safeHTMLToReact = (html: string, opts: { sanitizerOptions?: DomPurifyConfig } = {}) =>
  HTMLReactParser(sanitizeHTML(html, { ...defaultSanitizerOptions, ...opts.sanitizerOptions }));
