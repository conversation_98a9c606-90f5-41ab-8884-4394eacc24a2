/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { PrintingPreferencesBodyParameterSchema } from './printingPreferencesBodyParameterSchema';
import type { QueuedTaskSchema } from './queuedTaskSchema';

export type PostApiProjectsProjectIdIssuesIssueIdExportPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Request accepted
 */
export type PostApiProjectsProjectIdIssuesIssueIdExport202Schema = QueuedTaskSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdExport401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesIssueIdExport403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdExport404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdExportMutationRequestSchema = PrintingPreferencesBodyParameterSchema;

export type PostApiProjectsProjectIdIssuesIssueIdExportMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdExport202Schema;

export type PostApiProjectsProjectIdIssuesIssueIdExportSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdExport202Schema;
  Request: PostApiProjectsProjectIdIssuesIssueIdExportMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdExportPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdExport401Schema
    | PostApiProjectsProjectIdIssuesIssueIdExport403Schema
    | PostApiProjectsProjectIdIssuesIssueIdExport404Schema;
};
