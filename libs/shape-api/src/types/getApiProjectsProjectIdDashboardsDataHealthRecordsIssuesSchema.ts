/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DataHealthRecordsIssueListSchema } from './dataHealthRecordsIssueListSchema';
import type { ErrorSchema } from './errorSchema';

export type GetApiProjectsProjectIdDashboardsDataHealthRecordsIssuesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type GetApiProjectsProjectIdDashboardsDataHealthRecordsIssuesQueryParamsSchema = {
  /**
   * @type string | undefined, date
   */
  date_start?: string;
  /**
   * @type string | undefined, date
   */
  date_end?: string;
  /**
   * @type integer | undefined
   */
  team_member_id?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
};

/**
 * @description List of data health records
 */
export type GetApiProjectsProjectIdDashboardsDataHealthRecordsIssues200Schema = DataHealthRecordsIssueListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdDashboardsDataHealthRecordsIssues401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdDashboardsDataHealthRecordsIssues403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdDashboardsDataHealthRecordsIssues404Schema = unknown;

export type GetApiProjectsProjectIdDashboardsDataHealthRecordsIssuesQueryResponseSchema =
  GetApiProjectsProjectIdDashboardsDataHealthRecordsIssues200Schema;

export type GetApiProjectsProjectIdDashboardsDataHealthRecordsIssuesSchemaQuery = {
  Response: GetApiProjectsProjectIdDashboardsDataHealthRecordsIssues200Schema;
  PathParams: GetApiProjectsProjectIdDashboardsDataHealthRecordsIssuesPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdDashboardsDataHealthRecordsIssuesQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdDashboardsDataHealthRecordsIssues401Schema
    | GetApiProjectsProjectIdDashboardsDataHealthRecordsIssues403Schema
    | GetApiProjectsProjectIdDashboardsDataHealthRecordsIssues404Schema;
};
