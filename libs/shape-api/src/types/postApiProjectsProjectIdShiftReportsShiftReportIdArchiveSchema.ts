/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiProjectsProjectIdShiftReportsShiftReportIdArchivePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

/**
 * @description Shift report archived
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdArchive200Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdArchive401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdArchive403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdArchive404Schema = unknown;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdArchive422Schema = unknown;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdArchiveMutationResponseSchema =
  PostApiProjectsProjectIdShiftReportsShiftReportIdArchive200Schema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdArchiveSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftReportsShiftReportIdArchive200Schema;
  PathParams: PostApiProjectsProjectIdShiftReportsShiftReportIdArchivePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdArchive401Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdArchive403Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdArchive404Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdArchive422Schema;
};
