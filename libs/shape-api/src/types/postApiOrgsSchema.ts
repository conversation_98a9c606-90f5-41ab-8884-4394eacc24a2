/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { OrgSchema } from './orgSchema';

/**
 * @description Org created
 */
export type PostApiOrgs201Schema = OrgSchema;

/**
 * @description Authentication required
 */
export type PostApiOrgs401Schema = AuthenticationErrorSchema;

/**
 * @description Org not created
 */
export type PostApiOrgs422Schema = ErrorSchema;

export type PostApiOrgsMutationRequestSchema = {
  /**
   * @type object | undefined
   */
  org?: {
    /**
     * @type string | undefined
     */
    name?: string;
    /**
     * @type string | undefined
     */
    short_name?: string;
    /**
     * @type string | undefined
     */
    domain?: string;
    /**
     * @description The signed id given by the direct upload method
     * @type string | undefined
     */
    logo?: string;
    /**
     * @type string | undefined
     */
    verified_at?: string;
    /**
     * @type string | undefined
     */
    verification_token?: string;
    /**
     * @type string | undefined
     */
    verification_email?: string;
  };
};

export type PostApiOrgsMutationResponseSchema = PostApiOrgs201Schema;

export type PostApiOrgsSchemaMutation = {
  Response: PostApiOrgs201Schema;
  Request: PostApiOrgsMutationRequestSchema;
  Errors: PostApiOrgs401Schema | PostApiOrgs422Schema;
};
