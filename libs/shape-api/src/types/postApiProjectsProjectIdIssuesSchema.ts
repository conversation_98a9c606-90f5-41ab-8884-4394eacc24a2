/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueCategorySchema } from './issueCategorySchema';
import type { IssueSchema } from './issueSchema';
import type { IssueVisibilityStatusSchema } from './issueVisibilityStatusSchema';

export type PostApiProjectsProjectIdIssuesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type PostApiProjectsProjectIdIssuesHeaderParamsSchema = {
  /**
   * @description String to idenfiy the request. If the request is repeated with the same idempotency key,\n                              the same response will be returned.
   * @type string | undefined
   */
  'Idempotency-Key'?: string;
};

/**
 * @description Issue created
 */
export type PostApiProjectsProjectIdIssues201Schema = IssueSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssues401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssues403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type PostApiProjectsProjectIdIssues404Schema = unknown;

export type PostApiProjectsProjectIdIssuesMutationRequestSchema = {
  /**
   * @type object | undefined
   */
  issue?: {
    /**
     * @type array
     */
    approvers_attributes?:
      | {
          /**
           * @type string | undefined, uuid
           */
          id?: string;
          /**
           * @type integer | undefined
           */
          team_member_id?: number;
          /**
           * @type integer | undefined
           */
          sort_order?: number;
          /**
           * @type integer | undefined
           */
          _destroy?: number;
        }[]
      | null;
    category?: IssueCategorySchema | null;
    /**
     * @type boolean | undefined
     */
    critical?: boolean;
    /**
     * @type string
     */
    description?: string | null;
    /**
     * @type string, uuid
     */
    discipline_id?: string | null;
    /**
     * @type integer
     */
    draft_assignee_id?: number | null;
    /**
     * @type string, date-time
     */
    due_date?: string | null;
    /**
     * @type string
     */
    immediate_action?: string | null;
    /**
     * @type array
     */
    involved_teams_attributes?:
      | {
          /**
           * @type string | undefined, uuid
           */
          id?: string;
          /**
           * @type string | undefined
           */
          role?: string;
          /**
           * @type string | undefined, uuid
           */
          team_id?: string;
          /**
           * @type integer | undefined
           */
          _destroy?: number;
        }[]
      | null;
    /**
     * @type string, uuid
     */
    location_id?: string | null;
    /**
     * @type string
     */
    people_involved_safety?: string | null;
    /**
     * @type string, date-time
     */
    planned_closure_date?: string | null;
    /**
     * @type string
     */
    potential_impact_severity?: string | null;
    /**
     * @type string
     */
    preventative_action?: string | null;
    /**
     * @type boolean | undefined
     */
    safety_alert?: boolean;
    /**
     * @type integer
     */
    safety_likelihood_score?: number | null;
    /**
     * @type string
     */
    sub_category?: string | null;
    /**
     * @type string
     */
    title?: string | null;
    visibility_status?: IssueVisibilityStatusSchema | null;
  };
};

export type PostApiProjectsProjectIdIssuesMutationResponseSchema = PostApiProjectsProjectIdIssues201Schema;

export type PostApiProjectsProjectIdIssuesSchemaMutation = {
  Response: PostApiProjectsProjectIdIssues201Schema;
  Request: PostApiProjectsProjectIdIssuesMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdIssuesPathParamsSchema;
  HeaderParams: PostApiProjectsProjectIdIssuesHeaderParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssues401Schema
    | PostApiProjectsProjectIdIssues403Schema
    | PostApiProjectsProjectIdIssues404Schema;
};
