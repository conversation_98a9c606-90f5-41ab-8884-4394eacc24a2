/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueImageSchema } from './issueImageSchema';

export type PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
  /**
   * @type string, uuid
   */
  issue_image_id: string;
};

/**
 * @description Image updated
 * @deprecated
 */
export type PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageId200Schema = IssueImageSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageId403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageId404Schema = unknown;

export type PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  caption?: string;
};

export type PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdMutationResponseSchema =
  PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageId200Schema;

export type PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageId200Schema;
  Request: PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageId401Schema
    | PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageId403Schema
    | PatchApiProjectsProjectIdIssuesIssueIdIssueImagesIssueImageId404Schema;
};
