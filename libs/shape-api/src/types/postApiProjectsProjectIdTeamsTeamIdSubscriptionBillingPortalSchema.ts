/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamSubscriptionBillingPortalSchema } from './teamSubscriptionBillingPortalSchema';

export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortalPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
};

/**
 * @description Billing portal URL
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortal200Schema = TeamSubscriptionBillingPortalSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortal401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortal403Schema = ErrorSchema;

/**
 * @description Team not found
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortal404Schema = unknown;

/**
 * @description Billing portal not available
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortal422Schema = ErrorSchema;

export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortalMutationResponseSchema =
  PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortal200Schema;

export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortalSchemaMutation = {
  Response: PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortal200Schema;
  PathParams: PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortalPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortal401Schema
    | PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortal403Schema
    | PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortal404Schema
    | PostApiProjectsProjectIdTeamsTeamIdSubscriptionBillingPortal422Schema;
};
