/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadinessPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description Readiness updated
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadiness204Schema = unknown;

/**
 * @description Bad request
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadiness400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadiness401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadiness403Schema = unknown;

/**
 * @description Not found
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadiness404Schema = unknown;

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadinessMutationRequestSchema = {
  /**
   * @type boolean
   */
  ready: boolean;
};

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadinessMutationResponseSchema =
  PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadiness204Schema;

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadinessSchemaMutation = {
  Response: PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadiness204Schema;
  Request: PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadinessMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadinessPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadiness400Schema
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadiness401Schema
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadiness403Schema
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdReadiness404Schema;
};
