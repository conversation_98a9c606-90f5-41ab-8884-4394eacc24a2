/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const potentialChangeEstimatedScheduleImpactEnum = {
  none: 'none',
  minimal: 'minimal',
  short_term: 'short_term',
  medium_term: 'medium_term',
  long_term: 'long_term',
  extended_duration: 'extended_duration',
} as const;

export type PotentialChangeEstimatedScheduleImpactEnumSchema =
  (typeof potentialChangeEstimatedScheduleImpactEnum)[keyof typeof potentialChangeEstimatedScheduleImpactEnum];

export type PotentialChangeEstimatedScheduleImpactSchema = PotentialChangeEstimatedScheduleImpactEnumSchema;
