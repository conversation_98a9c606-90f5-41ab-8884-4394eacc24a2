/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { WeeklyWorkPlanListSchema } from './weeklyWorkPlanListSchema';
import type { WeeklyWorkPlanStatusesSchema } from './weeklyWorkPlanStatusesSchema';

export type GetApiProjectsProjectIdWeeklyWorkPlansPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export const getApiProjectsProjectIdWeeklyWorkPlansQueryParamsSortByEnum = {
  start_date: 'start_date',
  end_date: 'end_date',
} as const;

export type GetApiProjectsProjectIdWeeklyWorkPlansQueryParamsSortByEnumSchema =
  (typeof getApiProjectsProjectIdWeeklyWorkPlansQueryParamsSortByEnum)[keyof typeof getApiProjectsProjectIdWeeklyWorkPlansQueryParamsSortByEnum];

export const getApiProjectsProjectIdWeeklyWorkPlansQueryParamsSortOrderEnum = {
  asc: 'asc',
  desc: 'desc',
} as const;

export type GetApiProjectsProjectIdWeeklyWorkPlansQueryParamsSortOrderEnumSchema =
  (typeof getApiProjectsProjectIdWeeklyWorkPlansQueryParamsSortOrderEnum)[keyof typeof getApiProjectsProjectIdWeeklyWorkPlansQueryParamsSortOrderEnum];

export type GetApiProjectsProjectIdWeeklyWorkPlansQueryParamsSchema = {
  /**
   * @type integer | undefined
   */
  author_id?: number;
  status?: WeeklyWorkPlanStatusesSchema[] | WeeklyWorkPlanStatusesSchema;
  /**
   * @type string | undefined
   */
  sort_by?: GetApiProjectsProjectIdWeeklyWorkPlansQueryParamsSortByEnumSchema;
  /**
   * @description Filter by archived state
   * @type boolean | undefined
   */
  archived?: boolean;
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
  /**
   * @description Order of the results
   * @type string | undefined
   */
  sort_order?: GetApiProjectsProjectIdWeeklyWorkPlansQueryParamsSortOrderEnumSchema;
};

/**
 * @description List of weekly work plans
 */
export type GetApiProjectsProjectIdWeeklyWorkPlans200Schema = WeeklyWorkPlanListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdWeeklyWorkPlans400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdWeeklyWorkPlans401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdWeeklyWorkPlans403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdWeeklyWorkPlans404Schema = unknown;

export type GetApiProjectsProjectIdWeeklyWorkPlansQueryResponseSchema = GetApiProjectsProjectIdWeeklyWorkPlans200Schema;

export type GetApiProjectsProjectIdWeeklyWorkPlansSchemaQuery = {
  Response: GetApiProjectsProjectIdWeeklyWorkPlans200Schema;
  PathParams: GetApiProjectsProjectIdWeeklyWorkPlansPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdWeeklyWorkPlansQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdWeeklyWorkPlans400Schema
    | GetApiProjectsProjectIdWeeklyWorkPlans401Schema
    | GetApiProjectsProjectIdWeeklyWorkPlans403Schema
    | GetApiProjectsProjectIdWeeklyWorkPlans404Schema;
};
