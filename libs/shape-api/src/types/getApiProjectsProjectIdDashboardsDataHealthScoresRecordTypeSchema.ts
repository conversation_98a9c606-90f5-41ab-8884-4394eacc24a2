/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DataHealthDashboardScoreListSchema } from './dataHealthDashboardScoreListSchema';
import type { DataHealthRecordTypeSchema } from './dataHealthRecordTypeSchema';
import type { DataHealthTemporalScopeSchema } from './dataHealthTemporalScopeSchema';
import type { ErrorSchema } from './errorSchema';

export type GetApiProjectsProjectIdDashboardsDataHealthScoresRecordTypePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @description Allowed record types for data health scores
   * @type string
   */
  record_type: DataHealthRecordTypeSchema;
};

export type GetApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeQueryParamsSchema = {
  /**
   * @description The latest date to consider for evaluating health scores
   * @type string | undefined, date
   */
  latest_date?: string;
  /**
   * @description Number of discrete \"days\"/\"weeks\"/\"months\" to generate scores for
   * @type integer | undefined
   */
  series_length?: number;
  /**
   * @description Granularity of time used for aggregation
   * @type string | undefined
   */
  temporal_scope?: DataHealthTemporalScopeSchema;
};

/**
 * @description List of data health scores
 */
export type GetApiProjectsProjectIdDashboardsDataHealthScoresRecordType200Schema = DataHealthDashboardScoreListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdDashboardsDataHealthScoresRecordType401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdDashboardsDataHealthScoresRecordType403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdDashboardsDataHealthScoresRecordType404Schema = unknown;

export type GetApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeQueryResponseSchema =
  GetApiProjectsProjectIdDashboardsDataHealthScoresRecordType200Schema;

export type GetApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeSchemaQuery = {
  Response: GetApiProjectsProjectIdDashboardsDataHealthScoresRecordType200Schema;
  PathParams: GetApiProjectsProjectIdDashboardsDataHealthScoresRecordTypePathParamsSchema;
  QueryParams: GetApiProjectsProjectIdDashboardsDataHealthScoresRecordTypeQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdDashboardsDataHealthScoresRecordType401Schema
    | GetApiProjectsProjectIdDashboardsDataHealthScoresRecordType403Schema
    | GetApiProjectsProjectIdDashboardsDataHealthScoresRecordType404Schema;
};
