/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { UserOnboardingSchema } from './userOnboardingSchema';

/**
 * @description User onboarding
 */
export type GetApiOnboarding200Schema = UserOnboardingSchema;

/**
 * @description Authentication required
 */
export type GetApiOnboarding401Schema = AuthenticationErrorSchema;

/**
 * @description User onboarding not found
 */
export type GetApiOnboarding404Schema = unknown;

export type GetApiOnboardingQueryResponseSchema = GetApiOnboarding200Schema;

export type GetApiOnboardingSchemaQuery = {
  Response: GetApiOnboarding200Schema;
  Errors: GetApiOnboarding401Schema | GetApiOnboarding404Schema;
};
