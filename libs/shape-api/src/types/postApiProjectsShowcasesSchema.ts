/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { QueuedTaskSchema } from './queuedTaskSchema';

/**
 * @description Request accepted
 */
export type PostApiProjectsShowcases202Schema = QueuedTaskSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsShowcases401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type PostApiProjectsShowcases403Schema = ErrorSchema;

export type PostApiProjectsShowcasesMutationRequestSchema = {
  /**
   * @type string
   */
  title: string;
  /**
   * @type string
   */
  short_name: string;
  /**
   * @description The signed id given by direct upload
   * @type string | undefined
   */
  logo?: string;
  /**
   * @type string | undefined
   */
  timezone?: string;
};

export type PostApiProjectsShowcasesMutationResponseSchema = PostApiProjectsShowcases202Schema;

export type PostApiProjectsShowcasesSchemaMutation = {
  Response: PostApiProjectsShowcases202Schema;
  Request: PostApiProjectsShowcasesMutationRequestSchema;
  Errors: PostApiProjectsShowcases401Schema | PostApiProjectsShowcases403Schema;
};
