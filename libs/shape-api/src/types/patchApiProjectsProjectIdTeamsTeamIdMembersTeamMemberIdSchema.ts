/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamMemberRoleSchema } from './teamMemberRoleSchema';
import type { TeamMemberSchema } from './teamMemberSchema';

export type PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
  /**
   * @type integer
   */
  team_member_id: number;
};

/**
 * @description Team member updated
 */
export type PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberId200Schema = TeamMemberSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberId403Schema = ErrorSchema;

/**
 * @description Team member not found
 */
export type PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberId404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberId422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  role?: TeamMemberRoleSchema;
  /**
   * @type string
   */
  construction_role?: string | null;
};

export type PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMutationResponseSchema =
  PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberId200Schema;

export type PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberId200Schema;
  Request: PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberId401Schema
    | PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberId403Schema
    | PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberId404Schema
    | PatchApiProjectsProjectIdTeamsTeamIdMembersTeamMemberId422Schema;
};
