/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { OrgSchema } from './orgSchema';

export type PatchApiOrgsOrgIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  org_id: string;
};

/**
 * @description Org updated
 */
export type PatchApiOrgsOrgId200Schema = OrgSchema;

/**
 * @description Authentication required
 */
export type PatchApiOrgsOrgId401Schema = AuthenticationErrorSchema;

export type PatchApiOrgsOrgIdMutationRequestSchema = {
  /**
   * @type object | undefined
   */
  org?: {
    /**
     * @type string | undefined
     */
    name?: string;
    /**
     * @type string | undefined
     */
    short_name?: string;
    /**
     * @type string | undefined
     */
    domain?: string;
    /**
     * @description The signed id given by the direct upload method
     * @type string | undefined
     */
    logo?: string;
    /**
     * @type string | undefined
     */
    verified_at?: string;
    /**
     * @type string | undefined
     */
    verification_token?: string;
    /**
     * @type string | undefined
     */
    verification_email?: string;
  };
};

export type PatchApiOrgsOrgIdMutationResponseSchema = PatchApiOrgsOrgId200Schema;

export type PatchApiOrgsOrgIdSchemaMutation = {
  Response: PatchApiOrgsOrgId200Schema;
  Request: PatchApiOrgsOrgIdMutationRequestSchema;
  PathParams: PatchApiOrgsOrgIdPathParamsSchema;
  Errors: PatchApiOrgsOrgId401Schema;
};
