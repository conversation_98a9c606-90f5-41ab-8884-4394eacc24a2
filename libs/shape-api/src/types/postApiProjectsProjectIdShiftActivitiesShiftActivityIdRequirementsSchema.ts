/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftActivityRequirementSchema } from './shiftActivityRequirementSchema';

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description Shift activity requirement created
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements201Schema =
  ShiftActivityRequirementSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements422Schema = ErrorSchema;

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  title?: string;
};

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsMutationResponseSchema =
  PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements201Schema;

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements201Schema;
  Request: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements401Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements403Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements404Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements422Schema;
};
