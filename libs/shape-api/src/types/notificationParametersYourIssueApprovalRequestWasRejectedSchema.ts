/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersYourIssueApprovalRequestWasRejectedTypeEnum = {
  your_issue_approval_request_was_rejected: 'your_issue_approval_request_was_rejected',
} as const;

export type NotificationParametersYourIssueApprovalRequestWasRejectedTypeEnumSchema =
  (typeof notificationParametersYourIssueApprovalRequestWasRejectedTypeEnum)[keyof typeof notificationParametersYourIssueApprovalRequestWasRejectedTypeEnum];

export type NotificationParametersYourIssueApprovalRequestWasRejectedSchema = {
  /**
   * @type string
   */
  type: NotificationParametersYourIssueApprovalRequestWasRejectedTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type string, uuid
     */
    projectId: string;
    /**
     * @type string
     */
    reason: string;
  };
};
