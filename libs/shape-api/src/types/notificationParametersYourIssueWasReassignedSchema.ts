/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersYourIssueWasReassignedTypeEnum = {
  your_issue_was_reassigned: 'your_issue_was_reassigned',
} as const;

export type NotificationParametersYourIssueWasReassignedTypeEnumSchema =
  (typeof notificationParametersYourIssueWasReassignedTypeEnum)[keyof typeof notificationParametersYourIssueWasReassignedTypeEnum];

export type NotificationParametersYourIssueWasReassignedSchema = {
  /**
   * @type string
   */
  type: NotificationParametersYourIssueWasReassignedTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type string, uuid
     */
    projectId: string;
  };
};
