/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { GroupChannelConfigurationSchema } from './groupChannelConfigurationSchema';

export type GetApiProjectsProjectIdGroupsGroupIdChannelConfigurationPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  group_id: string;
};

/**
 * @description Group channel configuration
 */
export type GetApiProjectsProjectIdGroupsGroupIdChannelConfiguration200Schema = GroupChannelConfigurationSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdGroupsGroupIdChannelConfiguration401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdGroupsGroupIdChannelConfiguration403Schema = ErrorSchema;

/**
 * @description Group not found
 */
export type GetApiProjectsProjectIdGroupsGroupIdChannelConfiguration404Schema = unknown;

export type GetApiProjectsProjectIdGroupsGroupIdChannelConfigurationQueryResponseSchema =
  GetApiProjectsProjectIdGroupsGroupIdChannelConfiguration200Schema;

export type GetApiProjectsProjectIdGroupsGroupIdChannelConfigurationSchemaQuery = {
  Response: GetApiProjectsProjectIdGroupsGroupIdChannelConfiguration200Schema;
  PathParams: GetApiProjectsProjectIdGroupsGroupIdChannelConfigurationPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdGroupsGroupIdChannelConfiguration401Schema
    | GetApiProjectsProjectIdGroupsGroupIdChannelConfiguration403Schema
    | GetApiProjectsProjectIdGroupsGroupIdChannelConfiguration404Schema;
};
