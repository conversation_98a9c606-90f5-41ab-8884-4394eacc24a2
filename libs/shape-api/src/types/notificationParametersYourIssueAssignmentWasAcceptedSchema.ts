/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersYourIssueAssignmentWasAcceptedTypeEnum = {
  your_issue_assignment_was_accepted: 'your_issue_assignment_was_accepted',
} as const;

export type NotificationParametersYourIssueAssignmentWasAcceptedTypeEnumSchema =
  (typeof notificationParametersYourIssueAssignmentWasAcceptedTypeEnum)[keyof typeof notificationParametersYourIssueAssignmentWasAcceptedTypeEnum];

export type NotificationParametersYourIssueAssignmentWasAcceptedSchema = {
  /**
   * @type string
   */
  type: NotificationParametersYourIssueAssignmentWasAcceptedTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type string, uuid
     */
    projectId: string;
  };
};
