/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ShiftActivityProgressLogSchema } from './shiftActivityProgressLogSchema';

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
  /**
   * @type string, uuid
   */
  progress_log_id: string;
};

/**
 * @description Shift Activity Progress Log
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId200Schema =
  ShiftActivityProgressLogSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId404Schema = unknown;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdQueryResponseSchema =
  GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId200Schema;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId200Schema;
  PathParams: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId401Schema
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId404Schema;
};
