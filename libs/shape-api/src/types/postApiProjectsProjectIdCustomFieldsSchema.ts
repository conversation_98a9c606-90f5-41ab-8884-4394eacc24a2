/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { CustomFieldSchema } from './customFieldSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiProjectsProjectIdCustomFieldsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Custom field created
 */
export type PostApiProjectsProjectIdCustomFields201Schema = CustomFieldSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdCustomFields401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdCustomFields403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdCustomFields404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdCustomFields422Schema = ErrorSchema;

export type PostApiProjectsProjectIdCustomFieldsMutationRequestSchema = {
  /**
   * @type string
   */
  label: string;
  /**
   * @type boolean
   */
  project_wide: boolean;
};

export type PostApiProjectsProjectIdCustomFieldsMutationResponseSchema = PostApiProjectsProjectIdCustomFields201Schema;

export type PostApiProjectsProjectIdCustomFieldsSchemaMutation = {
  Response: PostApiProjectsProjectIdCustomFields201Schema;
  Request: PostApiProjectsProjectIdCustomFieldsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdCustomFieldsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdCustomFields401Schema
    | PostApiProjectsProjectIdCustomFields403Schema
    | PostApiProjectsProjectIdCustomFields404Schema
    | PostApiProjectsProjectIdCustomFields422Schema;
};
