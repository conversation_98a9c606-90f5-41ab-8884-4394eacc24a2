/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { TeamBasicDetailsSchema } from './teamBasicDetailsSchema';
import type { TeamMemberConstructionRoleSchema } from './teamMemberConstructionRoleSchema';
import type { TeamMemberRoleSchema } from './teamMemberRoleSchema';
import type { TeamMemberStatusSchema } from './teamMemberStatusSchema';
import type { UserBasicDetailsSchema } from './userBasicDetailsSchema';

export type TeamMemberSchema = {
  /**
   * @type integer
   */
  id: number;
  /**
   * @type string, uuid
   */
  newId: string;
  /**
   * @type object
   */
  user: UserBasicDetailsSchema;
  /**
   * @type object
   */
  team: TeamBasicDetailsSchema;
  /**
   * @type object | undefined
   */
  invite?: {
    /**
     * @type object
     */
    availableActions: {
      /**
       * @type boolean
       */
      resendEmail: boolean;
      /**
       * @type boolean
       */
      destroy: boolean;
    };
  };
  /**
   * @type string
   */
  status: TeamMemberStatusSchema;
  /**
   * @type string
   */
  role: TeamMemberRoleSchema;
  constructionRole: TeamMemberConstructionRoleSchema | null;
  /**
   * @type string, uuid
   */
  projectId: string;
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    edit: boolean;
    /**
     * @type boolean
     */
    archive: boolean;
  };
};
