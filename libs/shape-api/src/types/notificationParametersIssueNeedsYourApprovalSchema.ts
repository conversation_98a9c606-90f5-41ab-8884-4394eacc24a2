/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersIssueNeedsYourApprovalTypeEnum = {
  issue_needs_your_approval: 'issue_needs_your_approval',
} as const;

export type NotificationParametersIssueNeedsYourApprovalTypeEnumSchema =
  (typeof notificationParametersIssueNeedsYourApprovalTypeEnum)[keyof typeof notificationParametersIssueNeedsYourApprovalTypeEnum];

export type NotificationParametersIssueNeedsYourApprovalSchema = {
  /**
   * @type string
   */
  type: NotificationParametersIssueNeedsYourApprovalTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type string, uuid
     */
    projectId: string;
  };
};
