/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { CustomFieldListSchema } from './customFieldListSchema';
import type { ErrorSchema } from './errorSchema';

export type GetApiProjectsProjectIdCustomFieldsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description List of project custom fields
 */
export type GetApiProjectsProjectIdCustomFields200Schema = CustomFieldListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdCustomFields401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdCustomFields403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdCustomFields404Schema = unknown;

export type GetApiProjectsProjectIdCustomFieldsQueryResponseSchema = GetApiProjectsProjectIdCustomFields200Schema;

export type GetApiProjectsProjectIdCustomFieldsSchemaQuery = {
  Response: GetApiProjectsProjectIdCustomFields200Schema;
  PathParams: GetApiProjectsProjectIdCustomFieldsPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdCustomFields401Schema
    | GetApiProjectsProjectIdCustomFields403Schema
    | GetApiProjectsProjectIdCustomFields404Schema;
};
