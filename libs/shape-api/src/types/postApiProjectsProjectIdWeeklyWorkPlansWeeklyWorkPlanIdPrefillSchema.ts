/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { WeeklyWorkPlanSchema } from './weeklyWorkPlanSchema';

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefillPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

/**
 * @description Weekly Work Plan prefilled
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefill200Schema = WeeklyWorkPlanSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefill400Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefill401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefill403Schema = unknown;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefill404Schema = unknown;

/**
 * @description Prefill failed
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefill422Schema = unknown;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefillMutationRequestSchema = {
  /**
   * @type string, uuid
   */
  prefill_from_plan_id: string;
};

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefillMutationResponseSchema =
  PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefill200Schema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefillSchemaMutation = {
  Response: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefill200Schema;
  Request: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefillMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefillPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefill400Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefill401Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefill403Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefill404Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPrefill422Schema;
};
