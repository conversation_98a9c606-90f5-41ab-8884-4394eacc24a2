/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamSchema } from './teamSchema';

export type PostApiProjectsProjectIdTeamsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Team created
 */
export type PostApiProjectsProjectIdTeams201Schema = TeamSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdTeams401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdTeams403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type PostApiProjectsProjectIdTeams404Schema = unknown;

/**
 * @description Invalid request
 */
export type PostApiProjectsProjectIdTeams422Schema = ErrorSchema;

export type PostApiProjectsProjectIdTeamsMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  display_name?: string;
};

export type PostApiProjectsProjectIdTeamsMutationResponseSchema = PostApiProjectsProjectIdTeams201Schema;

export type PostApiProjectsProjectIdTeamsSchemaMutation = {
  Response: PostApiProjectsProjectIdTeams201Schema;
  Request: PostApiProjectsProjectIdTeamsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdTeamsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdTeams401Schema
    | PostApiProjectsProjectIdTeams403Schema
    | PostApiProjectsProjectIdTeams404Schema
    | PostApiProjectsProjectIdTeams422Schema;
};
