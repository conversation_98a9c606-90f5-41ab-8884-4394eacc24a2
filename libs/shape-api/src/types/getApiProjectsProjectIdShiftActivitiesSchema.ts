/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { OneOrManyIntegerNullableSchema } from './oneOrManyIntegerNullableSchema';
import type { OneOrManyUuidNullableSchema } from './oneOrManyUuidNullableSchema';
import type { OneOrManyUuidSchema } from './oneOrManyUuidSchema';
import type { ShiftActivityListSchema } from './shiftActivityListSchema';
import type { ShiftActivityStatusSchema } from './shiftActivityStatusSchema';

export type GetApiProjectsProjectIdShiftActivitiesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type GetApiProjectsProjectIdShiftActivitiesQueryParamsSchema = {
  id?: OneOrManyUuidSchema;
  /**
   * @type boolean | undefined
   */
  archived?: boolean;
  status?: ShiftActivityStatusSchema[] | ShiftActivityStatusSchema;
  assigned_team_member_id?: OneOrManyIntegerNullableSchema;
  location_id?: OneOrManyUuidNullableSchema;
  organisation_resource_id?: OneOrManyUuidNullableSchema;
  weekly_work_plan_id?: OneOrManyUuidNullableSchema;
  /**
   * @type boolean | undefined
   */
  critical?: boolean;
  owner_id?: OneOrManyIntegerNullableSchema;
  /**
   * @type string | undefined, date-time
   */
  planned_start_date_start?: string;
  /**
   * @type string | undefined, date-time
   */
  planned_start_date_end?: string;
  /**
   * @type string | undefined, date-time
   */
  planned_end_date_start?: string;
  /**
   * @type string | undefined, date-time
   */
  planned_end_date_end?: string;
  /**
   * @type string | undefined, date-time
   */
  expected_finish_date_start?: string;
  /**
   * @type string | undefined, date-time
   */
  expected_finish_date_end?: string;
  /**
   * @type string | undefined, date-time
   */
  actual_start_date_start?: string;
  /**
   * @type string | undefined, date-time
   */
  actual_start_date_end?: string;
  /**
   * @type string | undefined, date-time
   */
  actual_end_date_start?: string;
  /**
   * @type string | undefined, date-time
   */
  actual_end_date_end?: string;
  /**
   * @type boolean | undefined
   */
  ready?: boolean;
  /**
   * @type string | undefined
   */
  search?: string;
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of shift activities
 */
export type GetApiProjectsProjectIdShiftActivities200Schema = ShiftActivityListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdShiftActivities400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftActivities401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdShiftActivities403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftActivities404Schema = unknown;

export type GetApiProjectsProjectIdShiftActivitiesQueryResponseSchema = GetApiProjectsProjectIdShiftActivities200Schema;

export type GetApiProjectsProjectIdShiftActivitiesSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftActivities200Schema;
  PathParams: GetApiProjectsProjectIdShiftActivitiesPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdShiftActivitiesQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftActivities400Schema
    | GetApiProjectsProjectIdShiftActivities401Schema
    | GetApiProjectsProjectIdShiftActivities403Schema
    | GetApiProjectsProjectIdShiftActivities404Schema;
};
