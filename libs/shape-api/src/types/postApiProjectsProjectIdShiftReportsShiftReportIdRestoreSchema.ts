/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiProjectsProjectIdShiftReportsShiftReportIdRestorePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

/**
 * @description Shift report restored
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdRestore200Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdRestore401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdRestore403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdRestore404Schema = unknown;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdRestore422Schema = ErrorSchema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdRestoreMutationResponseSchema =
  PostApiProjectsProjectIdShiftReportsShiftReportIdRestore200Schema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdRestoreSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftReportsShiftReportIdRestore200Schema;
  PathParams: PostApiProjectsProjectIdShiftReportsShiftReportIdRestorePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdRestore401Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdRestore403Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdRestore404Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdRestore422Schema;
};
