/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ChangeSignalIssueListSchema } from './changeSignalIssueListSchema';

export type GetApiProjectsProjectIdControlCenterChangeSignalsIssuesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type GetApiProjectsProjectIdControlCenterChangeSignalsIssuesQueryParamsSchema = {
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of issue change signals
 */
export type GetApiProjectsProjectIdControlCenterChangeSignalsIssues200Schema = ChangeSignalIssueListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdControlCenterChangeSignalsIssues401Schema = AuthenticationErrorSchema;

/**
 * @description Project not found
 */
export type GetApiProjectsProjectIdControlCenterChangeSignalsIssues404Schema = unknown;

export type GetApiProjectsProjectIdControlCenterChangeSignalsIssuesQueryResponseSchema =
  GetApiProjectsProjectIdControlCenterChangeSignalsIssues200Schema;

export type GetApiProjectsProjectIdControlCenterChangeSignalsIssuesSchemaQuery = {
  Response: GetApiProjectsProjectIdControlCenterChangeSignalsIssues200Schema;
  PathParams: GetApiProjectsProjectIdControlCenterChangeSignalsIssuesPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdControlCenterChangeSignalsIssuesQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdControlCenterChangeSignalsIssues401Schema
    | GetApiProjectsProjectIdControlCenterChangeSignalsIssues404Schema;
};
