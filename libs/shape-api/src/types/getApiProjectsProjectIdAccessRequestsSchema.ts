/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ProjectAccessRequestListSchema } from './projectAccessRequestListSchema';

export type GetApiProjectsProjectIdAccessRequestsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type GetApiProjectsProjectIdAccessRequestsQueryParamsSchema = {
  /**
   * @description Page number
   * @type integer | undefined
   */
  page?: number;
};

/**
 * @description List of pending access requests
 */
export type GetApiProjectsProjectIdAccessRequests200Schema = ProjectAccessRequestListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdAccessRequests400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdAccessRequests401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdAccessRequests403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type GetApiProjectsProjectIdAccessRequests404Schema = unknown;

export type GetApiProjectsProjectIdAccessRequestsQueryResponseSchema = GetApiProjectsProjectIdAccessRequests200Schema;

export type GetApiProjectsProjectIdAccessRequestsSchemaQuery = {
  Response: GetApiProjectsProjectIdAccessRequests200Schema;
  PathParams: GetApiProjectsProjectIdAccessRequestsPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdAccessRequestsQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdAccessRequests400Schema
    | GetApiProjectsProjectIdAccessRequests401Schema
    | GetApiProjectsProjectIdAccessRequests403Schema
    | GetApiProjectsProjectIdAccessRequests404Schema;
};
