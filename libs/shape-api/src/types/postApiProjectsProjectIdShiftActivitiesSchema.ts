/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftActivitySchema } from './shiftActivitySchema';
import type { ShiftActivityStatusSchema } from './shiftActivityStatusSchema';

export type PostApiProjectsProjectIdShiftActivitiesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Shift activity created
 */
export type PostApiProjectsProjectIdShiftActivities201Schema = ShiftActivitySchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftActivities401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftActivities403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftActivities404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdShiftActivities422Schema = ErrorSchema;

export type PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema = {
  /**
   * @type string, date-time
   */
  actual_end_date?: string | null;
  /**
   * @type string, date-time
   */
  actual_start_date?: string | null;
  /**
   * @type integer
   */
  assigned_team_member_id?: number | null;
  /**
   * @type boolean | undefined
   */
  critical?: boolean;
  /**
   * @type string
   */
  description: string;
  /**
   * @type string, date-time
   */
  expected_finish_date?: string | null;
  /**
   * @type string, uuid
   */
  location_id?: string | null;
  /**
   * @type string, uuid
   */
  organisation_resource_id?: string | null;
  /**
   * @type integer
   */
  owner_id?: number | null;
  /**
   * @type string, date-time
   */
  planned_end_date?: string | null;
  /**
   * @type string, date-time
   */
  planned_start_date?: string | null;
  status?: ShiftActivityStatusSchema | null;
  /**
   * @type string
   */
  task_identifier?: string | null;
};

export type PostApiProjectsProjectIdShiftActivitiesMutationResponseSchema =
  PostApiProjectsProjectIdShiftActivities201Schema;

export type PostApiProjectsProjectIdShiftActivitiesSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftActivities201Schema;
  Request: PostApiProjectsProjectIdShiftActivitiesMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftActivitiesPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftActivities401Schema
    | PostApiProjectsProjectIdShiftActivities403Schema
    | PostApiProjectsProjectIdShiftActivities404Schema
    | PostApiProjectsProjectIdShiftActivities422Schema;
};
