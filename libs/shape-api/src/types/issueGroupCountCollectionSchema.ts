/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { IssueGroupCountEntitySchema } from './issueGroupCountEntitySchema';
import type { IssueGroupSchema } from './issueGroupSchema';

/**
 * @description Recursive groupCollection structure while it has sub-groups
 */
export type IssueGroupCountCollectionSchema = {
  /**
   * @type string
   */
  group: IssueGroupSchema;
  /**
   * @type string | undefined
   */
  groupLabel?: string;
  /**
   * @type string | undefined, uuid
   */
  customFieldId?: string;
  /**
   * @type array
   */
  groupEntities: IssueGroupCountEntitySchema[];
};
