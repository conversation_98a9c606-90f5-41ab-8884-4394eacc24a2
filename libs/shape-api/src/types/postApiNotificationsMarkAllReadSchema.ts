/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';

/**
 * @description Notifications updated
 */
export type PostApiNotificationsMarkAllRead204Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiNotificationsMarkAllRead401Schema = AuthenticationErrorSchema;

export type PostApiNotificationsMarkAllReadMutationResponseSchema = PostApiNotificationsMarkAllRead204Schema;

export type PostApiNotificationsMarkAllReadSchemaMutation = {
  Response: PostApiNotificationsMarkAllRead204Schema;
  Errors: PostApiNotificationsMarkAllRead401Schema;
};
