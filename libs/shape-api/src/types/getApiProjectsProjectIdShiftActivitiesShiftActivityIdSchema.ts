/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ShiftActivitySchema } from './shiftActivitySchema';

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description Shift Activity
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityId200Schema = ShiftActivitySchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityId401Schema = AuthenticationErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityId404Schema = unknown;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdQueryResponseSchema =
  GetApiProjectsProjectIdShiftActivitiesShiftActivityId200Schema;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftActivitiesShiftActivityId200Schema;
  PathParams: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityId401Schema
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityId404Schema;
};
