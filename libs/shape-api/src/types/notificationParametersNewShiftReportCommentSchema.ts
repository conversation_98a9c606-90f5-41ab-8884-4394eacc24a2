/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const paramsChannelEnum = {
  public: 'public',
  collaborators: 'collaborators',
} as const;

export type ParamsChannelEnumSchema = (typeof paramsChannelEnum)[keyof typeof paramsChannelEnum];

export type NotificationParametersNewShiftReportCommentSchema = {
  /**
   * @type string
   */
  type: string;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string
     */
    channel: ParamsChannelEnumSchema;
    /**
     * @type string, uuid
     */
    commentId: string;
    /**
     * @type string
     */
    message: string;
    /**
     * @type string, uuid
     */
    projectId: string;
    /**
     * @type string, date
     */
    shiftReportDate: string;
    /**
     * @type string, uuid
     */
    shiftReportId: string;
    /**
     * @type string
     */
    shiftReportTitle: string | null;
  };
};
