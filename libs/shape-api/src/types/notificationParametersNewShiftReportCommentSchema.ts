/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersNewShiftReportCommentTypeEnum = {
  new_shift_report_comment: 'new_shift_report_comment',
} as const;

export type NotificationParametersNewShiftReportCommentTypeEnumSchema =
  (typeof notificationParametersNewShiftReportCommentTypeEnum)[keyof typeof notificationParametersNewShiftReportCommentTypeEnum];

export const paramsChannelEnum = {
  public: 'public',
  collaborators: 'collaborators',
} as const;

export type ParamsChannelEnumSchema = (typeof paramsChannelEnum)[keyof typeof paramsChannelEnum];

export type NotificationParametersNewShiftReportCommentSchema = {
  /**
   * @type string
   */
  type: NotificationParametersNewShiftReportCommentTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string
     */
    channel: ParamsChannelEnumSchema;
    /**
     * @type string, uuid
     */
    commentId: string;
    /**
     * @type string
     */
    message: string;
    /**
     * @type string, uuid
     */
    projectId: string;
    /**
     * @type string, date
     */
    shiftReportDate: string;
    /**
     * @type string, uuid
     */
    shiftReportId: string;
    /**
     * @type string
     */
    shiftReportTitle: string | null;
  };
};
