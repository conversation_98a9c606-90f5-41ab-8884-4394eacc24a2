/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersAddedToProjectTypeEnum = {
  added_to_project: 'added_to_project',
} as const;

export type NotificationParametersAddedToProjectTypeEnumSchema =
  (typeof notificationParametersAddedToProjectTypeEnum)[keyof typeof notificationParametersAddedToProjectTypeEnum];

export type NotificationParametersAddedToProjectSchema = {
  /**
   * @type string
   */
  type: NotificationParametersAddedToProjectTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    projectId: string;
    /**
     * @type string
     */
    projectTitle: string;
  };
};
