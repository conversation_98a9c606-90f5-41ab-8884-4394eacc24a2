/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DirectUploadSchema } from './directUploadSchema';
import type { DirectUploadTypeSchema } from './directUploadTypeSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiDirectUploadsTypePathParamsSchema = {
  /**
   * @type string
   */
  type: DirectUploadTypeSchema;
};

/**
 * @description Direct upload blob
 */
export type PostApiDirectUploadsType200Schema = DirectUploadSchema;

/**
 * @description Direct upload failed
 */
export type PostApiDirectUploadsType400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiDirectUploadsType401Schema = AuthenticationErrorSchema;

/**
 * @description Not found
 */
export type PostApiDirectUploadsType404Schema = unknown;

export type PostApiDirectUploadsTypeMutationRequestSchema = {
  /**
   * @type object
   */
  blob: {
    /**
     * @type integer
     */
    byte_size: number;
    /**
     * @type string
     */
    checksum: string;
    /**
     * @type string | undefined
     */
    content_type?: string;
    /**
     * @type string
     */
    filename: string;
    /**
     * @type object | undefined
     */
    metadata?: object;
  };
};

export type PostApiDirectUploadsTypeMutationResponseSchema = PostApiDirectUploadsType200Schema;

export type PostApiDirectUploadsTypeSchemaMutation = {
  Response: PostApiDirectUploadsType200Schema;
  Request: PostApiDirectUploadsTypeMutationRequestSchema;
  PathParams: PostApiDirectUploadsTypePathParamsSchema;
  Errors: PostApiDirectUploadsType400Schema | PostApiDirectUploadsType401Schema | PostApiDirectUploadsType404Schema;
};
