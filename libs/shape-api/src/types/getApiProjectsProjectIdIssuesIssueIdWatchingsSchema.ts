/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { WatchingListSchema } from './watchingListSchema';

export type GetApiProjectsProjectIdIssuesIssueIdWatchingsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description List of watchings
 */
export type GetApiProjectsProjectIdIssuesIssueIdWatchings200Schema = WatchingListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdIssuesIssueIdWatchings401Schema = AuthenticationErrorSchema;

/**
 * @description Issue not found
 */
export type GetApiProjectsProjectIdIssuesIssueIdWatchings404Schema = unknown;

export type GetApiProjectsProjectIdIssuesIssueIdWatchingsQueryResponseSchema =
  GetApiProjectsProjectIdIssuesIssueIdWatchings200Schema;

export type GetApiProjectsProjectIdIssuesIssueIdWatchingsSchemaQuery = {
  Response: GetApiProjectsProjectIdIssuesIssueIdWatchings200Schema;
  PathParams: GetApiProjectsProjectIdIssuesIssueIdWatchingsPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdIssuesIssueIdWatchings401Schema
    | GetApiProjectsProjectIdIssuesIssueIdWatchings404Schema;
};
