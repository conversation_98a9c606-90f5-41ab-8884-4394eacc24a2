/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export type PostApiProjectsProjectIdDefaultPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Default project set
 */
export type PostApiProjectsProjectIdDefault204Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdDefault401Schema = unknown;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdDefault403Schema = unknown;

/**
 * @description Project not found
 */
export type PostApiProjectsProjectIdDefault404Schema = unknown;

export type PostApiProjectsProjectIdDefaultMutationResponseSchema = PostApiProjectsProjectIdDefault204Schema;

export type PostApiProjectsProjectIdDefaultSchemaMutation = {
  Response: PostApiProjectsProjectIdDefault204Schema;
  PathParams: PostApiProjectsProjectIdDefaultPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdDefault401Schema
    | PostApiProjectsProjectIdDefault403Schema
    | PostApiProjectsProjectIdDefault404Schema;
};
