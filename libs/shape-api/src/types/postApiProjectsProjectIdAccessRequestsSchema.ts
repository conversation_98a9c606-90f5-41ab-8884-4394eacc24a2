/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ProjectAccessRequestSchema } from './projectAccessRequestSchema';

export type PostApiProjectsProjectIdAccessRequestsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Project access request created
 */
export type PostApiProjectsProjectIdAccessRequests201Schema = ProjectAccessRequestSchema;

/**
 * @description User already in a project team
 */
export type PostApiProjectsProjectIdAccessRequests400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdAccessRequests401Schema = AuthenticationErrorSchema;

/**
 * @description Project not found
 */
export type PostApiProjectsProjectIdAccessRequests404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdAccessRequests422Schema = ErrorSchema;

export type PostApiProjectsProjectIdAccessRequestsMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  team_name?: string;
  /**
   * @type string
   */
  team_admin_email?: string | null;
  /**
   * @type string
   */
  message?: string | null;
};

export type PostApiProjectsProjectIdAccessRequestsMutationResponseSchema =
  PostApiProjectsProjectIdAccessRequests201Schema;

export type PostApiProjectsProjectIdAccessRequestsSchemaMutation = {
  Response: PostApiProjectsProjectIdAccessRequests201Schema;
  Request: PostApiProjectsProjectIdAccessRequestsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdAccessRequestsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdAccessRequests400Schema
    | PostApiProjectsProjectIdAccessRequests401Schema
    | PostApiProjectsProjectIdAccessRequests404Schema
    | PostApiProjectsProjectIdAccessRequests422Schema;
};
