/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamChannelConfigurationSchema } from './teamChannelConfigurationSchema';

export type GetApiProjectsProjectIdTeamsTeamIdChannelConfigurationPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
};

/**
 * @description Team channel configuration
 */
export type GetApiProjectsProjectIdTeamsTeamIdChannelConfiguration200Schema = TeamChannelConfigurationSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdTeamsTeamIdChannelConfiguration401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdTeamsTeamIdChannelConfiguration403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdTeamsTeamIdChannelConfiguration404Schema = unknown;

export type GetApiProjectsProjectIdTeamsTeamIdChannelConfigurationQueryResponseSchema =
  GetApiProjectsProjectIdTeamsTeamIdChannelConfiguration200Schema;

export type GetApiProjectsProjectIdTeamsTeamIdChannelConfigurationSchemaQuery = {
  Response: GetApiProjectsProjectIdTeamsTeamIdChannelConfiguration200Schema;
  PathParams: GetApiProjectsProjectIdTeamsTeamIdChannelConfigurationPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdTeamsTeamIdChannelConfiguration401Schema
    | GetApiProjectsProjectIdTeamsTeamIdChannelConfiguration403Schema
    | GetApiProjectsProjectIdTeamsTeamIdChannelConfiguration404Schema;
};
