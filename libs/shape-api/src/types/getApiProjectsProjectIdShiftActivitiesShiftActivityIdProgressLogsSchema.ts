/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ShiftActivityProgressLogListSchema } from './shiftActivityProgressLogListSchema';

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsQueryParamsSchema = {
  /**
   * @type boolean | undefined
   */
  include_extra_details?: boolean;
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of shift activity progress logs
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs200Schema =
  ShiftActivityProgressLogListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs401Schema = AuthenticationErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs404Schema = unknown;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsQueryResponseSchema =
  GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs200Schema;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs200Schema;
  PathParams: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs401Schema
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs404Schema;
};
