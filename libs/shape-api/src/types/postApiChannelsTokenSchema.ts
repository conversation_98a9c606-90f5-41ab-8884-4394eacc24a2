/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ChannelsTokenSchema } from './channelsTokenSchema';
import type { ErrorSchema } from './errorSchema';

/**
 * @description Channels token
 */
export type PostApiChannelsToken200Schema = ChannelsTokenSchema;

/**
 * @description Authentication required
 */
export type PostApiChannelsToken401Schema = AuthenticationErrorSchema;

/**
 * @description Request failed
 */
export type PostApiChannelsToken422Schema = ErrorSchema;

/**
 * @description Channels service not available
 */
export type PostApiChannelsToken503Schema = ErrorSchema;

export type PostApiChannelsTokenMutationResponseSchema = PostApiChannelsToken200Schema;

export type PostApiChannelsTokenSchemaMutation = {
  Response: PostApiChannelsToken200Schema;
  Errors: PostApiChannelsToken401Schema | PostApiChannelsToken422Schema | PostApiChannelsToken503Schema;
};
