/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueEventParametersCreateArrayItemSchema } from './issueEventParametersCreateArrayItemSchema';

export type IssueEventParametersCreateSchema = {
  /**
   * @type string
   */
  eventType: string;
  /**
   * @type object
   */
  parameters: {
    /**
     * @type array | undefined
     */
    issueImageIds?: number[];
    /**
     * @type array | undefined
     */
    imageUrls?: string[];
  };
  /**
   * @type object
   */
  resources: {
    /**
     * @type array
     */
    images: IssueEventParametersCreateArrayItemSchema[];
  };
};
