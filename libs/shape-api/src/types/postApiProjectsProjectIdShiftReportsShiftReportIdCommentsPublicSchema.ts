/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftReportCommentSchema } from './shiftReportCommentSchema';

export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

/**
 * @description Comment created
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic201Schema = ShiftReportCommentSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic403Schema = ErrorSchema;

/**
 * @description Shift report not found
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic404Schema = unknown;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicMutationRequestSchema = {
  /**
   * @description Comment body in plain text
   * @type string
   */
  plain_text: string;
  /**
   * @type array | undefined
   */
  mentioned_team_member_ids?: number[];
  /**
   * @description Comment body in rich text
   * @type object | undefined
   */
  rich_text?: object;
};

export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicMutationResponseSchema =
  PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic201Schema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic201Schema;
  Request: PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic400Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic401Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic403Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic404Schema;
};
