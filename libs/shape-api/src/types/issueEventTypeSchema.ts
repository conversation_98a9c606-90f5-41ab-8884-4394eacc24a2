/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export const issueEventTypeEnum = {
  accept_assignment: 'accept_assignment',
  add_approver: 'add_approver',
  add_team: 'add_team',
  approve: 'approve',
  archive: 'archive',
  assign: 'assign',
  change_status: 'change_status',
  comment_on: 'comment_on',
  create: 'create',
  delete_document: 'delete_document',
  delete_image: 'delete_image',
  delete_status_statement: 'delete_status_statement',
  private_comment_on: 'private_comment_on',
  reject_assignment: 'reject_assignment',
  reject_resolution: 'reject_resolution',
  remove_approver: 'remove_approver',
  remove_team: 'remove_team',
  reopen: 'reopen',
  restore: 'restore',
  update: 'update',
  update_image: 'update_image',
  update_impact: 'update_impact',
  update_observer: 'update_observer',
  update_status_statement: 'update_status_statement',
  upload_document: 'upload_document',
  upload_image: 'upload_image',
} as const;

export type IssueEventTypeEnumSchema = (typeof issueEventTypeEnum)[keyof typeof issueEventTypeEnum];

export type IssueEventTypeSchema = IssueEventTypeEnumSchema;
