/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { GroupSchema } from './groupSchema';

export type PatchApiProjectsProjectIdGroupsGroupIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  group_id: string;
};

/**
 * @description Group updated
 */
export type PatchApiProjectsProjectIdGroupsGroupId200Schema = GroupSchema;

/**
 * @description Bad request
 */
export type PatchApiProjectsProjectIdGroupsGroupId400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdGroupsGroupId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdGroupsGroupId403Schema = ErrorSchema;

/**
 * @description Group not found
 */
export type PatchApiProjectsProjectIdGroupsGroupId404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdGroupsGroupId422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdGroupsGroupIdMutationRequestSchema = {
  /**
   * @type string
   */
  name?: string | null;
  /**
   * @description The signed id given by the direct upload method
   * @type string
   */
  avatar?: string | null;
};

export type PatchApiProjectsProjectIdGroupsGroupIdMutationResponseSchema =
  PatchApiProjectsProjectIdGroupsGroupId200Schema;

export type PatchApiProjectsProjectIdGroupsGroupIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdGroupsGroupId200Schema;
  Request: PatchApiProjectsProjectIdGroupsGroupIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdGroupsGroupIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdGroupsGroupId400Schema
    | PatchApiProjectsProjectIdGroupsGroupId401Schema
    | PatchApiProjectsProjectIdGroupsGroupId403Schema
    | PatchApiProjectsProjectIdGroupsGroupId404Schema
    | PatchApiProjectsProjectIdGroupsGroupId422Schema;
};
