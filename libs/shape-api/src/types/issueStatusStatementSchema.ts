/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export type IssueStatusStatementSchema = {
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    delete: boolean;
    /**
     * @type string, date-time
     */
    gracePeriodUntil: string | null;
  };
  /**
   * @type string, date
   */
  date: string;
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  statement: string;
  /**
   * @type integer
   */
  teamMemberId: number;
};
