/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { IssueGroupCountCollectionSchema } from './issueGroupCountCollectionSchema';

export type IssueGroupCountEntitySchema = {
  identifier: (string | number) | null;
  /**
   * @type integer
   */
  totalCount: number;
  /**
   * @type object | undefined
   */
  innerEntity?: IssueGroupCountEntitySchema;
  /**
   * @type object | undefined
   */
  groupCollection?: IssueGroupCountCollectionSchema;
};
