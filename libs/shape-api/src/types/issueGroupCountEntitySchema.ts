/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueGroupCountCollectionSchema } from './issueGroupCountCollectionSchema';

export type IssueGroupCountEntitySchema = {
  identifier: (string | number) | null;
  /**
   * @type integer
   */
  totalCount: number;
  /**
   * @type object | undefined
   */
  innerEntity?: IssueGroupCountEntitySchema;
  /**
   * @description Recursive groupCollection structure while it has sub-groups
   * @type object | undefined
   */
  groupCollection?: IssueGroupCountCollectionSchema;
};
