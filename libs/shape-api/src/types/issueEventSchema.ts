/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { IssueEventParametersSchema } from './issueEventParametersSchema';
import type { IssueEventTypeSchema } from './issueEventTypeSchema';
import type { UserBasicDetailsSchema } from './userBasicDetailsSchema';

export type IssueEventSchema = {
  /**
   * @type string
   */
  body: string;
  /**
   * @type string, date-time
   */
  date: string;
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type object
   */
  owner: UserBasicDetailsSchema;
  /**
   * @type object | undefined
   */
  recipient?: UserBasicDetailsSchema;
  /**
   * @type string
   */
  type: IssueEventTypeSchema;
} & IssueEventParametersSchema;
