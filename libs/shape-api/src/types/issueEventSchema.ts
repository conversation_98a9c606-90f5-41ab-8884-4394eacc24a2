/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueEventParametersSchema } from './issueEventParametersSchema';
import type { IssueEventTypeSchema } from './issueEventTypeSchema';
import type { UserBasicDetailsSchema } from './userBasicDetailsSchema';

export type IssueEventSchema = IssueEventParametersSchema & {
  /**
   * @type string
   */
  body: string;
  /**
   * @type string, date-time
   */
  date: string;
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type object
   */
  owner: UserBasicDetailsSchema;
  /**
   * @type object | undefined
   */
  recipient?: UserBasicDetailsSchema;
  /**
   * @type string
   */
  type: IssueEventTypeSchema;
};
