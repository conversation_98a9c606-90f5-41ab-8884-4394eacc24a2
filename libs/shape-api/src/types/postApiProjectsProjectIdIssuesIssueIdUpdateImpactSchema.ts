/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueImpactSchema } from './issueImpactSchema';
import type { IssueSchema } from './issueSchema';

export type PostApiProjectsProjectIdIssuesIssueIdUpdateImpactPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue updated
 */
export type PostApiProjectsProjectIdIssuesIssueIdUpdateImpact200Schema = IssueSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdUpdateImpact401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesIssueIdUpdateImpact403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdUpdateImpact404Schema = unknown;

/**
 * @description Unable to update
 */
export type PostApiProjectsProjectIdIssuesIssueIdUpdateImpact422Schema = ErrorSchema;

export type PostApiProjectsProjectIdIssuesIssueIdUpdateImpactMutationRequestSchema = {
  /**
   * @type object | undefined
   */
  issue?: {
    /**
     * @type string, date-time
     */
    delay_start?: string | null;
    /**
     * @type string, date-time
     */
    delay_finish?: string | null;
    /**
     * @type string | undefined
     */
    impact?: IssueImpactSchema;
    /**
     * @type string
     */
    work_affected?: string | null;
    /**
     * @type string, date-time
     */
    due_date?: string | null;
  };
};

export type PostApiProjectsProjectIdIssuesIssueIdUpdateImpactMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdUpdateImpact200Schema;

export type PostApiProjectsProjectIdIssuesIssueIdUpdateImpactSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdUpdateImpact200Schema;
  Request: PostApiProjectsProjectIdIssuesIssueIdUpdateImpactMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdUpdateImpactPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdUpdateImpact401Schema
    | PostApiProjectsProjectIdIssuesIssueIdUpdateImpact403Schema
    | PostApiProjectsProjectIdIssuesIssueIdUpdateImpact404Schema
    | PostApiProjectsProjectIdIssuesIssueIdUpdateImpact422Schema;
};
