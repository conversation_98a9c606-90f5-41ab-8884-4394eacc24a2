/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ResourceKindSchema } from './resourceKindSchema';
import type { TruncatedResourceListSchema } from './truncatedResourceListSchema';

export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
  /**
   * @type string
   */
  kind: ResourceKindSchema;
};

export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindQueryParamsSchema = {
  /**
   * @type string | undefined
   */
  search?: string;
};

/**
 * @description Resources
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind200Schema = TruncatedResourceListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind404Schema = unknown;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindQueryResponseSchema =
  GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind200Schema;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind200Schema;
  PathParams: GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind401Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind403Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind404Schema;
};
