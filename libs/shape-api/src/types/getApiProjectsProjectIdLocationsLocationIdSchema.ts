/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { LocationSchema } from './locationSchema';

export type GetApiProjectsProjectIdLocationsLocationIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  location_id: string;
};

/**
 * @description Location found
 */
export type GetApiProjectsProjectIdLocationsLocationId200Schema = LocationSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdLocationsLocationId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type GetApiProjectsProjectIdLocationsLocationId403Schema = ErrorSchema;

/**
 * @description Location not found
 */
export type GetApiProjectsProjectIdLocationsLocationId404Schema = unknown;

export type GetApiProjectsProjectIdLocationsLocationIdQueryResponseSchema =
  GetApiProjectsProjectIdLocationsLocationId200Schema;

export type GetApiProjectsProjectIdLocationsLocationIdSchemaQuery = {
  Response: GetApiProjectsProjectIdLocationsLocationId200Schema;
  PathParams: GetApiProjectsProjectIdLocationsLocationIdPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdLocationsLocationId401Schema
    | GetApiProjectsProjectIdLocationsLocationId403Schema
    | GetApiProjectsProjectIdLocationsLocationId404Schema;
};
