/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const shiftActivityOverviewDailyProgressEntryShiftReportPartialEntryTypeEnum = {
  shift_report: 'shift_report',
} as const;

export type ShiftActivityOverviewDailyProgressEntryShiftReportPartialEntryTypeEnumSchema =
  (typeof shiftActivityOverviewDailyProgressEntryShiftReportPartialEntryTypeEnum)[keyof typeof shiftActivityOverviewDailyProgressEntryShiftReportPartialEntryTypeEnum];

export type ShiftActivityOverviewDailyProgressEntryShiftReportPartialSchema = {
  /**
   * @type string
   */
  entryType: ShiftActivityOverviewDailyProgressEntryShiftReportPartialEntryTypeEnumSchema;
  /**
   * @type string, uuid
   */
  shiftReportActivityId: string;
  /**
   * @type string, uuid
   */
  shiftReportId: string;
};
