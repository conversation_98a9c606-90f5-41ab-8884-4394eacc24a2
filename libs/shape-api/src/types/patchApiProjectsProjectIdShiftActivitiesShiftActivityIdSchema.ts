/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftActivitySchema } from './shiftActivitySchema';
import type { ShiftActivityStatusSchema } from './shiftActivityStatusSchema';

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description Shift activity updated
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityId200Schema = ShiftActivitySchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityId403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityId404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityId422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdMutationRequestSchema = {
  /**
   * @type string, date-time
   */
  actual_end_date?: string | null;
  /**
   * @type string, date-time
   */
  actual_start_date?: string | null;
  /**
   * @type integer
   */
  assigned_team_member_id?: number | null;
  /**
   * @type boolean | undefined
   */
  critical?: boolean;
  /**
   * @type string | undefined
   */
  description?: string;
  /**
   * @type string, date-time
   */
  expected_finish_date?: string | null;
  /**
   * @type string, uuid
   */
  location_id?: string | null;
  /**
   * @type string, uuid
   */
  organisation_resource_id?: string | null;
  /**
   * @type integer
   */
  owner_id?: number | null;
  /**
   * @type string, date-time
   */
  planned_end_date?: string | null;
  /**
   * @type string, date-time
   */
  planned_start_date?: string | null;
  status?: ShiftActivityStatusSchema | null;
  /**
   * @type string
   */
  task_identifier?: string | null;
};

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdMutationResponseSchema =
  PatchApiProjectsProjectIdShiftActivitiesShiftActivityId200Schema;

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdShiftActivitiesShiftActivityId200Schema;
  Request: PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityId401Schema
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityId403Schema
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityId404Schema
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityId422Schema;
};
