/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DocumentReferenceAndDocumentSchema } from './documentReferenceAndDocumentSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiProjectsProjectIdIssuesIssueIdDocumentsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Document added
 */
export type PostApiProjectsProjectIdIssuesIssueIdDocuments201Schema = DocumentReferenceAndDocumentSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdIssuesIssueIdDocuments400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdDocuments401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesIssueIdDocuments403Schema = ErrorSchema;

/**
 * @description Invalid document
 */
export type PostApiProjectsProjectIdIssuesIssueIdDocuments422Schema = ErrorSchema;

export type PostApiProjectsProjectIdIssuesIssueIdDocumentsMutationRequestSchema = {
  /**
   * @description The id of an existing document
   * @type string | undefined, uuid
   */
  document_id?: string;
  /**
   * @description The signed id given by the direct upload method
   * @type string | undefined
   */
  signed_id?: string;
  /**
   * @type string
   */
  caption?: string | null;
  /**
   * @type boolean | undefined
   */
  completion_evidence?: boolean;
  /**
   * @type string, uuid
   */
  location_id?: string | null;
};

export type PostApiProjectsProjectIdIssuesIssueIdDocumentsMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdDocuments201Schema;

export type PostApiProjectsProjectIdIssuesIssueIdDocumentsSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdDocuments201Schema;
  Request: PostApiProjectsProjectIdIssuesIssueIdDocumentsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdDocumentsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdDocuments400Schema
    | PostApiProjectsProjectIdIssuesIssueIdDocuments401Schema
    | PostApiProjectsProjectIdIssuesIssueIdDocuments403Schema
    | PostApiProjectsProjectIdIssuesIssueIdDocuments422Schema;
};
