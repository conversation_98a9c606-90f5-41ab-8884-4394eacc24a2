/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { OrgDomainCheckSchema } from './orgDomainCheckSchema';

/**
 * @description Domain availability
 */
export type PostApiOrgsCheckDomain200Schema = OrgDomainCheckSchema;

/**
 * @description No domain checked
 */
export type PostApiOrgsCheckDomain204Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiOrgsCheckDomain401Schema = AuthenticationErrorSchema;

export type PostApiOrgsCheckDomainMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  domain?: string;
};

export type PostApiOrgsCheckDomainMutationResponseSchema =
  | PostApiOrgsCheckDomain200Schema
  | PostApiOrgsCheckDomain204Schema;

export type PostApiOrgsCheckDomainSchemaMutation = {
  Response: PostApiOrgsCheckDomain200Schema | PostApiOrgsCheckDomain204Schema;
  Request: PostApiOrgsCheckDomainMutationRequestSchema;
  Errors: PostApiOrgsCheckDomain401Schema;
};
