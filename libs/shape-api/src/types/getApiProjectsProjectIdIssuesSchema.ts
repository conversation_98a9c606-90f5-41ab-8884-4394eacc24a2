/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueCategorySchema } from './issueCategorySchema';
import type { IssueImpactSchema } from './issueImpactSchema';
import type { IssueListSchema } from './issueListSchema';
import type { IssueStateSchema } from './issueStateSchema';
import type { IssueVisibilityStatusSchema } from './issueVisibilityStatusSchema';

export type GetApiProjectsProjectIdIssuesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export const getApiProjectsProjectIdIssuesQueryParamsSortByEnum = {
  created: 'created',
  delay_finish: 'delay_finish',
  delay_start: 'delay_start',
  due_date: 'due_date',
  impact: 'impact',
  planned_date: 'planned_date',
  published: 'published',
  reference: 'reference',
  resolved: 'resolved',
  state: 'state',
  title: 'title',
  updated: 'updated',
} as const;

export type GetApiProjectsProjectIdIssuesQueryParamsSortByEnumSchema =
  (typeof getApiProjectsProjectIdIssuesQueryParamsSortByEnum)[keyof typeof getApiProjectsProjectIdIssuesQueryParamsSortByEnum];

export const getApiProjectsProjectIdIssuesQueryParamsSortOrderEnum = {
  asc: 'asc',
  desc: 'desc',
} as const;

export type GetApiProjectsProjectIdIssuesQueryParamsSortOrderEnumSchema =
  (typeof getApiProjectsProjectIdIssuesQueryParamsSortOrderEnum)[keyof typeof getApiProjectsProjectIdIssuesQueryParamsSortOrderEnum];

export type GetApiProjectsProjectIdIssuesQueryParamsSchema = {
  /**
   * @type string | undefined
   */
  sort_by?: GetApiProjectsProjectIdIssuesQueryParamsSortByEnumSchema;
  /**
   * @description Filter by archived state
   * @type boolean | undefined
   */
  archived?: boolean;
  /**
   * @description Filter issues associated with the team member
   * @type integer | undefined
   */
  associated_with?: number;
  /**
   * @description Filter by category
   * @type string | undefined
   */
  category?: IssueCategorySchema;
  /**
   * @description Filter issues closed on or before the date
   * @type string | undefined, date
   */
  closed_date_end?: string;
  /**
   * @description Filter issues closed on or after the date
   * @type string | undefined, date
   */
  closed_date_start?: string;
  /**
   * @description Filter by critical
   * @type boolean | undefined
   */
  critical?: boolean;
  /**
   * @description Used together with `custom_field_value`
   * @type string | undefined, uuid
   */
  custom_field_id?: string;
  /**
   * @description Used together with `custom_field_id`
   * @type string | undefined
   */
  custom_field_value?: string;
  /**
   * @description Filter by exact discipline node
   * @type string | undefined, uuid
   */
  discipline?: string;
  /**
   * @description Filter by discipline including its sub disciplines
   * @type string | undefined, uuid
   */
  discipline_branch?: string;
  /**
   * @description Filter issues due on or before the date
   * @type string | undefined, date
   */
  due_date_end?: string;
  /**
   * @description Filter issues due on or after the date
   * @type string | undefined, date
   */
  due_date_start?: string;
  /**
   * @description Filter by excluding state
   * @type string | undefined
   */
  filter_out_state?: IssueStateSchema;
  /**
   * @description Filter by state (\"filter_state\" is also aliased as \"state\")
   * @type string | undefined
   */
  filter_state?: IssueStateSchema;
  /**
   * @description Filter by impact
   * @type string | undefined
   */
  impact?: IssueImpactSchema;
  /**
   * @description Filter by involved team id (to be used together with visibility_status)
   * @type string | undefined, uuid
   */
  issue_involved_team_id?: string;
  /**
   * @description Filter by exact location node
   * @type string | undefined, uuid
   */
  location?: string;
  /**
   * @description Filter by location including its sub locations
   * @type string | undefined, uuid
   */
  location_branch?: string;
  /**
   * @description Filter by next actioner id
   * @type integer | undefined
   */
  next_actioner?: number;
  /**
   * @description Filter by observer id
   * @type integer | undefined
   */
  observer?: number;
  /**
   * @description Filter by observer team id
   * @type string | undefined, uuid
   */
  observer_team?: string;
  /**
   * @description Filter by overdue
   * @type boolean | undefined
   */
  overdue?: boolean;
  /**
   * @description Page number
   * @type integer | undefined
   */
  page?: number;
  /**
   * @description Filter issues planned closure on or before the date
   * @type string | undefined, date
   */
  planned_closure_date_end?: string;
  /**
   * @description Filter issues planned closure on or after the date
   * @type string | undefined, date
   */
  planned_closure_date_start?: string;
  /**
   * @description Filter issues published on or before the date
   * @type string | undefined, date
   */
  published_date_end?: string;
  /**
   * @description Filter issues published on or after the date
   * @type string | undefined, date
   */
  published_date_start?: string;
  /**
   * @description Filter by responsible team member id
   * @type integer | undefined
   */
  responsible?: number;
  /**
   * @description Filter by responsible team id
   * @type string | undefined, uuid
   */
  responsible_team?: string;
  /**
   * @description Generic search query
   * @type string | undefined
   */
  search?: string;
  /**
   * @description Order of the results
   * @type string | undefined
   */
  sort_order?: GetApiProjectsProjectIdIssuesQueryParamsSortOrderEnumSchema;
  /**
   * @description Filter by state (\"state\" is also aliased as \"filter_state\")
   * @type string | undefined
   */
  state?: IssueStateSchema;
  /**
   * @description Filter by sub category
   * @type string | undefined
   */
  sub_category?: string;
  /**
   * @description Filter by team id
   * @type string | undefined, uuid
   */
  team_issues?: string;
  /**
   * @description Filter issues updated on or before the date
   * @type string | undefined, date
   */
  updated_date_end?: string;
  /**
   * @description Filter issues updated on or after the date
   * @type string | undefined, date
   */
  updated_date_start?: string;
  /**
   * @description Filter by team member id
   * @type integer | undefined
   */
  user_issues?: number;
  /**
   * @description Filter by visibility status
   * @type string | undefined
   */
  visibility_status?: IssueVisibilityStatusSchema;
  /**
   * @description Filter by watching team member id
   * @type integer | undefined
   */
  watching?: number;
};

/**
 * @description List of issues
 */
export type GetApiProjectsProjectIdIssues200Schema = IssueListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdIssues400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdIssues401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdIssues403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type GetApiProjectsProjectIdIssues404Schema = unknown;

export type GetApiProjectsProjectIdIssuesQueryResponseSchema = GetApiProjectsProjectIdIssues200Schema;

export type GetApiProjectsProjectIdIssuesSchemaQuery = {
  Response: GetApiProjectsProjectIdIssues200Schema;
  PathParams: GetApiProjectsProjectIdIssuesPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdIssuesQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdIssues400Schema
    | GetApiProjectsProjectIdIssues401Schema
    | GetApiProjectsProjectIdIssues403Schema
    | GetApiProjectsProjectIdIssues404Schema;
};
