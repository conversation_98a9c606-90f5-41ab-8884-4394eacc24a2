/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DocumentKindSchema } from './documentKindSchema';
import type { DocumentReferenceAndDocumentListSchema } from './documentReferenceAndDocumentListSchema';
import type { ErrorSchema } from './errorSchema';
import type { OneOrManyIntegerSchema } from './oneOrManyIntegerSchema';
import type { OneOrManyUuidNullableSchema } from './oneOrManyUuidNullableSchema';

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
  /**
   * @type string, uuid
   */
  progress_log_id: string;
};

export const getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsQueryParamsSortOrderEnum =
  {
    asc: 'asc',
    desc: 'desc',
  } as const;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsQueryParamsSortOrderEnumSchema =
  (typeof getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsQueryParamsSortOrderEnum)[keyof typeof getApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsQueryParamsSortOrderEnum];

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsQueryParamsSchema = {
  /**
   * @type string | undefined, date-time
   */
  created_end?: string;
  /**
   * @type string | undefined, date-time
   */
  created_start?: string;
  /**
   * @type string | undefined
   */
  kind?: DocumentKindSchema;
  location_id?: OneOrManyUuidNullableSchema;
  team_member_id?: OneOrManyIntegerSchema;
  /**
   * @description Generic search query
   * @type string | undefined
   */
  search?: string;
  /**
   * @description Order of the results
   * @type string | undefined
   */
  sort_order?: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsQueryParamsSortOrderEnumSchema;
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of shift activity progress log documents
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocuments200Schema =
  DocumentReferenceAndDocumentListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocuments400Schema =
  ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocuments401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocuments404Schema = unknown;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsQueryResponseSchema =
  GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocuments200Schema;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocuments200Schema;
  PathParams: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocumentsQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocuments400Schema
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocuments401Schema
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdDocuments404Schema;
};
