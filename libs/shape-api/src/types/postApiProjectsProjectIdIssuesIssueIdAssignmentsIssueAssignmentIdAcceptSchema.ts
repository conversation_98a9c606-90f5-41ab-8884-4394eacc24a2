/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueAssignmentSchema } from './issueAssignmentSchema';

export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAcceptPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
  /**
   * @type string, uuid
   */
  issue_assignment_id: string;
};

/**
 * @description Issue assignment accepted
 */
export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAccept200Schema = IssueAssignmentSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAccept401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAccept403Schema = ErrorSchema;

/**
 * @description Issue assignment not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAccept404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAcceptMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAccept200Schema;

export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAcceptSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAccept200Schema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAcceptPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAccept401Schema
    | PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAccept403Schema
    | PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdAccept404Schema;
};
