/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ChangeSignalDowntimeListSchema } from './changeSignalDowntimeListSchema';

export type GetApiProjectsProjectIdControlCenterChangeSignalsDowntimesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type GetApiProjectsProjectIdControlCenterChangeSignalsDowntimesQueryParamsSchema = {
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of downtime change signals
 */
export type GetApiProjectsProjectIdControlCenterChangeSignalsDowntimes200Schema = ChangeSignalDowntimeListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdControlCenterChangeSignalsDowntimes401Schema = AuthenticationErrorSchema;

/**
 * @description Project not found
 */
export type GetApiProjectsProjectIdControlCenterChangeSignalsDowntimes404Schema = unknown;

export type GetApiProjectsProjectIdControlCenterChangeSignalsDowntimesQueryResponseSchema =
  GetApiProjectsProjectIdControlCenterChangeSignalsDowntimes200Schema;

export type GetApiProjectsProjectIdControlCenterChangeSignalsDowntimesSchemaQuery = {
  Response: GetApiProjectsProjectIdControlCenterChangeSignalsDowntimes200Schema;
  PathParams: GetApiProjectsProjectIdControlCenterChangeSignalsDowntimesPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdControlCenterChangeSignalsDowntimesQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdControlCenterChangeSignalsDowntimes401Schema
    | GetApiProjectsProjectIdControlCenterChangeSignalsDowntimes404Schema;
};
