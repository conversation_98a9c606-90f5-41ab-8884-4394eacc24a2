/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const weeklyWorkPlanActivityStatusesEnum = {
  planned: 'planned',
  fallback: 'fallback',
  additional: 'additional',
} as const;

export type WeeklyWorkPlanActivityStatusesEnumSchema =
  (typeof weeklyWorkPlanActivityStatusesEnum)[keyof typeof weeklyWorkPlanActivityStatusesEnum];

export type WeeklyWorkPlanActivityStatusesSchema = WeeklyWorkPlanActivityStatusesEnumSchema;
