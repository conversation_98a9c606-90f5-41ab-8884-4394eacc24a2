/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const queuedTaskResultShowcaseProjectIdOperationEnum = {
  showcase_project_creation: 'showcase_project_creation',
} as const;

export type QueuedTaskResultShowcaseProjectIdOperationEnumSchema =
  (typeof queuedTaskResultShowcaseProjectIdOperationEnum)[keyof typeof queuedTaskResultShowcaseProjectIdOperationEnum];

export type QueuedTaskResultShowcaseProjectIdSchema = {
  /**
   * @type string
   */
  operation: QueuedTaskResultShowcaseProjectIdOperationEnumSchema;
  /**
   * @type object
   */
  result: {
    /**
     * @type string
     */
    projectId: string;
  } | null;
};
