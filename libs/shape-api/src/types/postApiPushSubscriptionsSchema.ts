/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { PushSubscriptionSchema } from './pushSubscriptionSchema';

/**
 * @description Push subscription created
 */
export type PostApiPushSubscriptions201Schema = PushSubscriptionSchema;

/**
 * @description Authentication required
 */
export type PostApiPushSubscriptions401Schema = AuthenticationErrorSchema;

/**
 * @description Push subscription not created
 */
export type PostApiPushSubscriptions422Schema = ErrorSchema;

export type PostApiPushSubscriptionsMutationRequestSchema = {
  /**
   * @type string
   */
  endpoint: string;
  /**
   * @type object
   */
  keys: {
    /**
     * @type string
     */
    p256dh: string;
    /**
     * @type string
     */
    auth: string;
  };
};

export type PostApiPushSubscriptionsMutationResponseSchema = PostApiPushSubscriptions201Schema;

export type PostApiPushSubscriptionsSchemaMutation = {
  Response: PostApiPushSubscriptions201Schema;
  Request: PostApiPushSubscriptionsMutationRequestSchema;
  Errors: PostApiPushSubscriptions401Schema | PostApiPushSubscriptions422Schema;
};
