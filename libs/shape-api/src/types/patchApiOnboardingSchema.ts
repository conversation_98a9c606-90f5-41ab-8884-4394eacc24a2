/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { UserOnboardingSchema } from './userOnboardingSchema';

/**
 * @description User onboarding updated
 */
export type PatchApiOnboarding200Schema = UserOnboardingSchema;

/**
 * @description Authentication required
 */
export type PatchApiOnboarding401Schema = AuthenticationErrorSchema;

/**
 * @description User onboarding not found
 */
export type PatchApiOnboarding404Schema = unknown;

export type PatchApiOnboardingMutationRequestSchema = {
  /**
   * @type object | undefined
   */
  data?: object;
  /**
   * @type string | undefined
   */
  step?: string;
  /**
   * @description The signed id given by the direct upload
   * @type string | undefined
   */
  project_logo?: string;
};

export type PatchApiOnboardingMutationResponseSchema = PatchApiOnboarding200Schema;

export type PatchApiOnboardingSchemaMutation = {
  Response: PatchApiOnboarding200Schema;
  Request: PatchApiOnboardingMutationRequestSchema;
  Errors: PatchApiOnboarding401Schema | PatchApiOnboarding404Schema;
};
