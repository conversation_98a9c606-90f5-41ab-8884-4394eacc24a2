/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ConstructionRoleListSchema } from './constructionRoleListSchema';

/**
 * @description List of construction roles
 */
export type GetApiConstructionRoles200Schema = ConstructionRoleListSchema;

/**
 * @description Authentication required
 */
export type GetApiConstructionRoles401Schema = AuthenticationErrorSchema;

export type GetApiConstructionRolesQueryResponseSchema = GetApiConstructionRoles200Schema;

export type GetApiConstructionRolesSchemaQuery = {
  Response: GetApiConstructionRoles200Schema;
  Errors: GetApiConstructionRoles401Schema;
};
