/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { NewOrExistingDocumentWithAttributesBodyParameterSchema } from './newOrExistingDocumentWithAttributesBodyParameterSchema';
import type { ShiftActivityProgressLogSchema } from './shiftActivityProgressLogSchema';

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description Shift activity progress log created
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs201Schema =
  ShiftActivityProgressLogSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs422Schema = ErrorSchema;

export const postApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMutationRequestTrackedInTypeEnum = {
  shift_report: 'shift_report',
  weekly_work_plan: 'weekly_work_plan',
} as const;

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMutationRequestTrackedInTypeEnumSchema =
  (typeof postApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMutationRequestTrackedInTypeEnum)[keyof typeof postApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMutationRequestTrackedInTypeEnum];

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  comment?: string;
  /**
   * @type string | undefined
   */
  description?: string;
  /**
   * @type string, date
   */
  date: string;
  /**
   * @type number, float
   */
  percentage_completed: number;
  /**
   * @type number | undefined, float
   */
  quantity?: number;
  /**
   * @type string | undefined
   */
  units?: string;
  /**
   * @type array | undefined
   */
  documents?: NewOrExistingDocumentWithAttributesBodyParameterSchema[];
  /**
   * @type string | undefined, uuid
   */
  tracked_in_id?: string;
  /**
   * @type string | undefined
   */
  tracked_in_type?: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMutationRequestTrackedInTypeEnumSchema;
};

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMutationResponseSchema =
  PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs201Schema;

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs201Schema;
  Request: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs401Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs403Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs404Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogs422Schema;
};
