/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueSchema } from './issueSchema';

export type PostApiProjectsProjectIdIssuesIssueIdArchivePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue archived
 */
export type PostApiProjectsProjectIdIssuesIssueIdArchive200Schema = IssueSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdArchive401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesIssueIdArchive403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdArchive404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdArchiveMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  reason?: string;
};

export type PostApiProjectsProjectIdIssuesIssueIdArchiveMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdArchive200Schema;

export type PostApiProjectsProjectIdIssuesIssueIdArchiveSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdArchive200Schema;
  Request: PostApiProjectsProjectIdIssuesIssueIdArchiveMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdArchivePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdArchive401Schema
    | PostApiProjectsProjectIdIssuesIssueIdArchive403Schema
    | PostApiProjectsProjectIdIssuesIssueIdArchive404Schema;
};
