/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamJoinTokenSchema } from './teamJoinTokenSchema';

export type GetApiProjectsProjectIdTeamsTeamIdJoinTokenPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
};

/**
 * @description Team join token
 */
export type GetApiProjectsProjectIdTeamsTeamIdJoinToken200Schema = TeamJoinTokenSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdTeamsTeamIdJoinToken401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdTeamsTeamIdJoinToken403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdTeamsTeamIdJoinToken404Schema = unknown;

export type GetApiProjectsProjectIdTeamsTeamIdJoinTokenQueryResponseSchema =
  GetApiProjectsProjectIdTeamsTeamIdJoinToken200Schema;

export type GetApiProjectsProjectIdTeamsTeamIdJoinTokenSchemaQuery = {
  Response: GetApiProjectsProjectIdTeamsTeamIdJoinToken200Schema;
  PathParams: GetApiProjectsProjectIdTeamsTeamIdJoinTokenPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdTeamsTeamIdJoinToken401Schema
    | GetApiProjectsProjectIdTeamsTeamIdJoinToken403Schema
    | GetApiProjectsProjectIdTeamsTeamIdJoinToken404Schema;
};
