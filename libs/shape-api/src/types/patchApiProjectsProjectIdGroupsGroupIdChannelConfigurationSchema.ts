/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { GroupChannelConfigurationSchema } from './groupChannelConfigurationSchema';

export type PatchApiProjectsProjectIdGroupsGroupIdChannelConfigurationPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  group_id: string;
};

/**
 * @description Updated group channel configuration
 */
export type PatchApiProjectsProjectIdGroupsGroupIdChannelConfiguration200Schema = GroupChannelConfigurationSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdGroupsGroupIdChannelConfiguration401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdGroupsGroupIdChannelConfiguration403Schema = ErrorSchema;

/**
 * @description Group not found
 */
export type PatchApiProjectsProjectIdGroupsGroupIdChannelConfiguration404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdGroupsGroupIdChannelConfiguration422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdGroupsGroupIdChannelConfigurationMutationRequestSchema = {
  /**
   * @type boolean | undefined
   */
  auto_upload_documents?: boolean;
};

export type PatchApiProjectsProjectIdGroupsGroupIdChannelConfigurationMutationResponseSchema =
  PatchApiProjectsProjectIdGroupsGroupIdChannelConfiguration200Schema;

export type PatchApiProjectsProjectIdGroupsGroupIdChannelConfigurationSchemaMutation = {
  Response: PatchApiProjectsProjectIdGroupsGroupIdChannelConfiguration200Schema;
  Request: PatchApiProjectsProjectIdGroupsGroupIdChannelConfigurationMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdGroupsGroupIdChannelConfigurationPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdGroupsGroupIdChannelConfiguration401Schema
    | PatchApiProjectsProjectIdGroupsGroupIdChannelConfiguration403Schema
    | PatchApiProjectsProjectIdGroupsGroupIdChannelConfiguration404Schema
    | PatchApiProjectsProjectIdGroupsGroupIdChannelConfiguration422Schema;
};
