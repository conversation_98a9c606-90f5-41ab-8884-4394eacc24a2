/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamMemberSchema } from './teamMemberSchema';

export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchivePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
  /**
   * @type integer
   */
  team_member_id: number;
};

/**
 * @description Archived team member
 */
export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchive200Schema = TeamMemberSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchive401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchive403Schema = ErrorSchema;

/**
 * @description Team member not found
 */
export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchive404Schema = unknown;

/**
 * @description Archive failed
 */
export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchive422Schema = ErrorSchema;

export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchiveMutationRequestSchema = {
  /**
   * @type integer | undefined
   */
  substitute_team_member_id?: number;
};

export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchiveMutationResponseSchema =
  PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchive200Schema;

export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchiveSchemaMutation = {
  Response: PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchive200Schema;
  Request: PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchiveMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchivePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchive401Schema
    | PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchive403Schema
    | PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchive404Schema
    | PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdArchive422Schema;
};
