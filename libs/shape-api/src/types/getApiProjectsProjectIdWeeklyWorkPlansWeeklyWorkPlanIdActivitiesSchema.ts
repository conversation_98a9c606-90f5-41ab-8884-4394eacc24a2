/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { WeeklyWorkPlanActivityListSchema } from './weeklyWorkPlanActivityListSchema';

export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryParamsSchema = {
  /**
   * @type string | undefined, date
   */
  scheduled_date?: string;
  /**
   * @type string | undefined
   */
  search?: string;
};

/**
 * @description List weekly work plan activities
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities200Schema =
  WeeklyWorkPlanActivityListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities403Schema = unknown;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities404Schema = unknown;

export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryResponseSchema =
  GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities200Schema;

export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSchemaQuery = {
  Response: GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities200Schema;
  PathParams: GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities401Schema
    | GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities403Schema
    | GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivities404Schema;
};
