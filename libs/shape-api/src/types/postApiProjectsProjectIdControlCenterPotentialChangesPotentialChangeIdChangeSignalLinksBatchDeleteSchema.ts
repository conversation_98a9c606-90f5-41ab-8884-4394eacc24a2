/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ChangeSignalsBodyParameterSchema } from './changeSignalsBodyParameterSchema';
import type { PotentialChangeSchema } from './potentialChangeSchema';

export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeletePathParamsSchema =
  {
    /**
     * @type string, uuid
     */
    project_id: string;
    /**
     * @type string, uuid
     */
    potential_change_id: string;
  };

/**
 * @description Deletes change signals links from a potential change
 */
export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDelete200Schema =
  PotentialChangeSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDelete401Schema =
  AuthenticationErrorSchema;

export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMutationRequestSchema =
  ChangeSignalsBodyParameterSchema;

export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMutationResponseSchema =
  PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDelete200Schema;

export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteSchemaMutation =
  {
    Response: PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDelete200Schema;
    Request: PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeleteMutationRequestSchema;
    PathParams: PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDeletePathParamsSchema;
    Errors: PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchDelete401Schema;
  };
