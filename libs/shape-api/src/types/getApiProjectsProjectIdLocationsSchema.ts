/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { LocationListSchema } from './locationListSchema';

export type GetApiProjectsProjectIdLocationsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export const getApiProjectsProjectIdLocationsQueryParamsHasDocumentsEnum = {
  true: true,
} as const;

export type GetApiProjectsProjectIdLocationsQueryParamsHasDocumentsEnumSchema =
  (typeof getApiProjectsProjectIdLocationsQueryParamsHasDocumentsEnum)[keyof typeof getApiProjectsProjectIdLocationsQueryParamsHasDocumentsEnum];

export const getApiProjectsProjectIdLocationsQueryParamsIncludeParentsEnum = {
  true: true,
} as const;

export type GetApiProjectsProjectIdLocationsQueryParamsIncludeParentsEnumSchema =
  (typeof getApiProjectsProjectIdLocationsQueryParamsIncludeParentsEnum)[keyof typeof getApiProjectsProjectIdLocationsQueryParamsIncludeParentsEnum];

export type GetApiProjectsProjectIdLocationsQueryParamsSchema = {
  /**
   * @description If true, filter locations that have associated documents
   * @type boolean | undefined
   */
  has_documents?: GetApiProjectsProjectIdLocationsQueryParamsHasDocumentsEnumSchema;
  /**
   * @description If true, include parents of filtered locations
   * @type boolean | undefined
   */
  include_parents?: GetApiProjectsProjectIdLocationsQueryParamsIncludeParentsEnumSchema;
  /**
   * @type string | undefined
   */
  search?: string;
};

/**
 * @description List of locations
 */
export type GetApiProjectsProjectIdLocations200Schema = LocationListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdLocations401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdLocations403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type GetApiProjectsProjectIdLocations404Schema = unknown;

export type GetApiProjectsProjectIdLocationsQueryResponseSchema = GetApiProjectsProjectIdLocations200Schema;

export type GetApiProjectsProjectIdLocationsSchemaQuery = {
  Response: GetApiProjectsProjectIdLocations200Schema;
  PathParams: GetApiProjectsProjectIdLocationsPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdLocationsQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdLocations401Schema
    | GetApiProjectsProjectIdLocations403Schema
    | GetApiProjectsProjectIdLocations404Schema;
};
