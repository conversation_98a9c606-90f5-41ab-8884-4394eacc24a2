/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ShiftActivityRequirementListSchema } from './shiftActivityRequirementListSchema';

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description List of shift activity requirements
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements200Schema =
  ShiftActivityRequirementListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements401Schema = AuthenticationErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements404Schema = unknown;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsQueryResponseSchema =
  GetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements200Schema;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements200Schema;
  PathParams: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements401Schema
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirements404Schema;
};
