/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueSchema } from './issueSchema';

export type PostApiProjectsProjectIdIssuesIssueIdRejectPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue updated
 */
export type PostApiProjectsProjectIdIssuesIssueIdReject200Schema = IssueSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdIssuesIssueIdReject400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdReject401Schema = AuthenticationErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdReject404Schema = unknown;

export const issueUserRejectResolveStatusEnum = {
  assigned: 'assigned',
  in_progress: 'in_progress',
} as const;

export type IssueUserRejectResolveStatusEnumSchema =
  (typeof issueUserRejectResolveStatusEnum)[keyof typeof issueUserRejectResolveStatusEnum];

export type PostApiProjectsProjectIdIssuesIssueIdRejectMutationRequestSchema = {
  /**
   * @type object | undefined
   */
  issue?: {
    /**
     * @type string
     */
    reject_reason?: string | null;
    /**
     * @type string | undefined
     */
    user_reject_resolve_status?: IssueUserRejectResolveStatusEnumSchema;
  };
};

export type PostApiProjectsProjectIdIssuesIssueIdRejectMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdReject200Schema;

export type PostApiProjectsProjectIdIssuesIssueIdRejectSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdReject200Schema;
  Request: PostApiProjectsProjectIdIssuesIssueIdRejectMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdRejectPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdReject400Schema
    | PostApiProjectsProjectIdIssuesIssueIdReject401Schema
    | PostApiProjectsProjectIdIssuesIssueIdReject404Schema;
};
