/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueSchema } from './issueSchema';

export type PostApiProjectsProjectIdIssuesIssueIdRestorePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue restored
 */
export type PostApiProjectsProjectIdIssuesIssueIdRestore200Schema = IssueSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdRestore401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesIssueIdRestore403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdRestore404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdRestoreMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdRestore200Schema;

export type PostApiProjectsProjectIdIssuesIssueIdRestoreSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdRestore200Schema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdRestorePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdRestore401Schema
    | PostApiProjectsProjectIdIssuesIssueIdRestore403Schema
    | PostApiProjectsProjectIdIssuesIssueIdRestore404Schema;
};
