/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { PotentialChangeListSchema } from './potentialChangeListSchema';

export type GetApiProjectsProjectIdControlCenterPotentialChangesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type GetApiProjectsProjectIdControlCenterPotentialChangesQueryParamsSchema = {
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of potential changes
 */
export type GetApiProjectsProjectIdControlCenterPotentialChanges200Schema = PotentialChangeListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdControlCenterPotentialChanges401Schema = AuthenticationErrorSchema;

/**
 * @description Project not found
 */
export type GetApiProjectsProjectIdControlCenterPotentialChanges404Schema = unknown;

export type GetApiProjectsProjectIdControlCenterPotentialChangesQueryResponseSchema =
  GetApiProjectsProjectIdControlCenterPotentialChanges200Schema;

export type GetApiProjectsProjectIdControlCenterPotentialChangesSchemaQuery = {
  Response: GetApiProjectsProjectIdControlCenterPotentialChanges200Schema;
  PathParams: GetApiProjectsProjectIdControlCenterPotentialChangesPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdControlCenterPotentialChangesQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdControlCenterPotentialChanges401Schema
    | GetApiProjectsProjectIdControlCenterPotentialChanges404Schema;
};
