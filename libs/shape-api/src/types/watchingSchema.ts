/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { UserBasicDetailsSchema } from './userBasicDetailsSchema';

export type WatchingSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string, uuid
   */
  issueId: string;
  /**
   * @type integer
   */
  teamMemberId: number;
  /**
   * @type object
   */
  userDetails: UserBasicDetailsSchema;
  /**
   * @type string, uuid
   */
  userId: string;
};
