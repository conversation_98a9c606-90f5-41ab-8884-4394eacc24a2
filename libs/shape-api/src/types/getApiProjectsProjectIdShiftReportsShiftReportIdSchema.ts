/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftReportSchema } from './shiftReportSchema';

export type GetApiProjectsProjectIdShiftReportsShiftReportIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

/**
 * @description Shift report
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportId200Schema = ShiftReportSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportId403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportId404Schema = unknown;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdQueryResponseSchema =
  GetApiProjectsProjectIdShiftReportsShiftReportId200Schema;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftReportsShiftReportId200Schema;
  PathParams: GetApiProjectsProjectIdShiftReportsShiftReportIdPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftReportsShiftReportId401Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportId403Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportId404Schema;
};
