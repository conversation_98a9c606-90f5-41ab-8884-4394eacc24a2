/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DocumentReferenceAndDocumentSchema } from './documentReferenceAndDocumentSchema';
import type { ErrorSchema } from './errorSchema';
import type { NewOrExistingDocumentWithAttributesBodyParameterSchema } from './newOrExistingDocumentWithAttributesBodyParameterSchema';

export type PostApiProjectsProjectIdShiftReportsShiftReportIdDocumentsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

/**
 * @description Document added
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdDocuments201Schema = DocumentReferenceAndDocumentSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdDocuments400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdDocuments401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdDocuments403Schema = ErrorSchema;

/**
 * @description Invalid document
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdDocuments422Schema = ErrorSchema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdDocumentsMutationRequestSchema =
  NewOrExistingDocumentWithAttributesBodyParameterSchema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdDocumentsMutationResponseSchema =
  PostApiProjectsProjectIdShiftReportsShiftReportIdDocuments201Schema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdDocumentsSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftReportsShiftReportIdDocuments201Schema;
  Request: PostApiProjectsProjectIdShiftReportsShiftReportIdDocumentsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftReportsShiftReportIdDocumentsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdDocuments400Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdDocuments401Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdDocuments403Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdDocuments422Schema;
};
