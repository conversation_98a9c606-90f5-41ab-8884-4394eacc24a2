/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { DocumentReferenceSchema } from './documentReferenceSchema';
import type { DocumentSchema } from './documentSchema';

export type IssueEventParametersUploadDocumentSchema = {
  /**
   * @type string
   */
  eventType: string;
  /**
   * @type object
   */
  parameters: {
    /**
     * @type string
     */
    filename: string;
    /**
     * @type string | undefined, uuid
     */
    issueDocumentId?: string;
  };
  /**
   * @type object
   */
  resources: {
    document: DocumentSchema | null;
    documentReference: DocumentReferenceSchema | null;
  };
};
