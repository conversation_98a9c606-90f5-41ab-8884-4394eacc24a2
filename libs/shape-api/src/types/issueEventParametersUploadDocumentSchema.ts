/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { DocumentReferenceSchema } from './documentReferenceSchema';
import type { DocumentSchema } from './documentSchema';

export const issueEventParametersUploadDocumentEventTypeEnum = {
  upload_document: 'upload_document',
} as const;

export type IssueEventParametersUploadDocumentEventTypeEnumSchema =
  (typeof issueEventParametersUploadDocumentEventTypeEnum)[keyof typeof issueEventParametersUploadDocumentEventTypeEnum];

export type IssueEventParametersUploadDocumentSchema = {
  /**
   * @type string
   */
  eventType: IssueEventParametersUploadDocumentEventTypeEnumSchema;
  /**
   * @type object
   */
  parameters: {
    /**
     * @type string
     */
    filename: string;
    /**
     * @type string | undefined, uuid
     */
    issueDocumentId?: string;
  };
  /**
   * @type object
   */
  resources: {
    document: DocumentSchema | null;
    documentReference: DocumentReferenceSchema | null;
  };
};
