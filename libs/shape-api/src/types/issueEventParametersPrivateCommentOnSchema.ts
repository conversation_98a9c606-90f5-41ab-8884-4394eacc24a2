/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { CommentSchema } from './commentSchema';

export type IssueEventParametersPrivateCommentOnSchema = {
  /**
   * @type string
   */
  eventType: string;
  /**
   * @type object
   */
  parameters: {
    /**
     * @type string
     */
    body: string;
    /**
     * @type string | undefined, uuid
     */
    commentId?: string;
  };
  /**
   * @type object
   */
  resources: {
    /**
     * @type object
     */
    comment: CommentSchema;
  };
};
