/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DashboardIssuesStalenessesCountSchema } from './dashboardIssuesStalenessesCountSchema';
import type { ErrorSchema } from './errorSchema';

export type GetApiProjectsProjectIdDashboardsIssuesStalenessCountPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Count
 */
export type GetApiProjectsProjectIdDashboardsIssuesStalenessCount200Schema = DashboardIssuesStalenessesCountSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdDashboardsIssuesStalenessCount401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdDashboardsIssuesStalenessCount403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdDashboardsIssuesStalenessCount404Schema = unknown;

export type GetApiProjectsProjectIdDashboardsIssuesStalenessCountQueryResponseSchema =
  GetApiProjectsProjectIdDashboardsIssuesStalenessCount200Schema;

export type GetApiProjectsProjectIdDashboardsIssuesStalenessCountSchemaQuery = {
  Response: GetApiProjectsProjectIdDashboardsIssuesStalenessCount200Schema;
  PathParams: GetApiProjectsProjectIdDashboardsIssuesStalenessCountPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdDashboardsIssuesStalenessCount401Schema
    | GetApiProjectsProjectIdDashboardsIssuesStalenessCount403Schema
    | GetApiProjectsProjectIdDashboardsIssuesStalenessCount404Schema;
};
