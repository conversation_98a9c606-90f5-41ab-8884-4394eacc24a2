/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { UserSchema } from './userSchema';

/**
 * @description User updated
 */
export type PatchApiUsersMe200Schema = UserSchema;

/**
 * @description Authentication required
 */
export type PatchApiUsersMe401Schema = AuthenticationErrorSchema;

/**
 * @description Update failed
 */
export type PatchApiUsersMe422Schema = ErrorSchema;

export type PatchApiUsersMeMutationRequestSchema = {
  /**
   * @type object
   */
  user: {
    /**
     * @description The signed id given by the direct upload method
     * @type string | undefined
     */
    avatar?: string;
    /**
     * @type string | undefined
     */
    current_password?: string;
    /**
     * @type string | undefined
     */
    email?: string;
    /**
     * @type string | undefined
     */
    first_name?: string;
    /**
     * @type string | undefined
     */
    last_name?: string;
    /**
     * @type string | undefined
     */
    password?: string;
    /**
     * @type boolean | undefined
     */
    reset_unconfirmed_email?: boolean;
  };
};

export type PatchApiUsersMeMutationResponseSchema = PatchApiUsersMe200Schema;

export type PatchApiUsersMeSchemaMutation = {
  Response: PatchApiUsersMe200Schema;
  Request: PatchApiUsersMeMutationRequestSchema;
  Errors: PatchApiUsersMe401Schema | PatchApiUsersMe422Schema;
};
