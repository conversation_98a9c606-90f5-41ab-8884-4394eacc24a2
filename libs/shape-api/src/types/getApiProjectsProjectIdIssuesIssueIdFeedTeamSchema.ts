/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueFeedSchema } from './issueFeedSchema';

export type GetApiProjectsProjectIdIssuesIssueIdFeedTeamPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

export type GetApiProjectsProjectIdIssuesIssueIdFeedTeamQueryParamsSchema = {
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of team issue events
 */
export type GetApiProjectsProjectIdIssuesIssueIdFeedTeam200Schema = IssueFeedSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdIssuesIssueIdFeedTeam400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdIssuesIssueIdFeedTeam401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type GetApiProjectsProjectIdIssuesIssueIdFeedTeam403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type GetApiProjectsProjectIdIssuesIssueIdFeedTeam404Schema = unknown;

export type GetApiProjectsProjectIdIssuesIssueIdFeedTeamQueryResponseSchema =
  GetApiProjectsProjectIdIssuesIssueIdFeedTeam200Schema;

export type GetApiProjectsProjectIdIssuesIssueIdFeedTeamSchemaQuery = {
  Response: GetApiProjectsProjectIdIssuesIssueIdFeedTeam200Schema;
  PathParams: GetApiProjectsProjectIdIssuesIssueIdFeedTeamPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdIssuesIssueIdFeedTeamQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdIssuesIssueIdFeedTeam400Schema
    | GetApiProjectsProjectIdIssuesIssueIdFeedTeam401Schema
    | GetApiProjectsProjectIdIssuesIssueIdFeedTeam403Schema
    | GetApiProjectsProjectIdIssuesIssueIdFeedTeam404Schema;
};
