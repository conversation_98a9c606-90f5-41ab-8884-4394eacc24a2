/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { FeatureFlagErrorSchema } from './featureFlagErrorSchema';
import type { FeatureFlagListSchema } from './featureFlagListSchema';

export type GetApiFeatureFlagsQueryParamsSchema = {
  /**
   * @type string | undefined
   */
  device_session_id?: string;
};

/**
 * @description List of flags
 */
export type GetApiFeatureFlags200Schema = FeatureFlagListSchema;

/**
 * @description Service not available
 */
export type GetApiFeatureFlags422Schema = FeatureFlagErrorSchema;

export type GetApiFeatureFlagsQueryResponseSchema = GetApiFeatureFlags200Schema;

export type GetApiFeatureFlagsSchemaQuery = {
  Response: GetApiFeatureFlags200Schema;
  QueryParams: GetApiFeatureFlagsQueryParamsSchema;
  Errors: GetApiFeatureFlags422Schema;
};
