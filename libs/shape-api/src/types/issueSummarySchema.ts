/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { IssueStateSchema } from './issueStateSchema';

export type IssueSummarySchema = {
  /**
   * @type boolean
   */
  archived: boolean;
  /**
   * @type string
   */
  currentState: IssueStateSchema;
  /**
   * @type string
   */
  description: string | null;
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type integer
   */
  observerId: number;
  /**
   * @type integer
   */
  qualityScore: number | null;
  /**
   * @type string
   */
  referenceNumber: string | null;
  /**
   * @type string
   */
  title: string | null;
};
