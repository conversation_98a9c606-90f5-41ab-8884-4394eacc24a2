/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueEventUpdateDateTimeChangeParametersSchema } from './issueEventUpdateDateTimeChangeParametersSchema';
import type { IssueEventUpdateStringChangeParametersSchema } from './issueEventUpdateStringChangeParametersSchema';
import type { IssueImpactSchema } from './issueImpactSchema';

export const categoryToEnum = {
  'close out': 'close out',
  'something needed': 'something needed',
  progress: 'progress',
  safety: 'safety',
} as const;

export type CategoryToEnumSchema = (typeof categoryToEnum)[keyof typeof categoryToEnum];

export const categoryFromEnum = {
  'close out': 'close out',
  'something needed': 'something needed',
  progress: 'progress',
  safety: 'safety',
} as const;

export type CategoryFromEnumSchema = (typeof categoryFromEnum)[keyof typeof categoryFromEnum];

export type IssueEventParametersUpdateSchema = {
  /**
   * @type string
   */
  eventType: string;
  /**
   * @type object
   */
  parameters: {
    /**
     * @type object | undefined
     */
    changes?: {
      /**
       * @type object | undefined
       */
      category?: {
        /**
         * @type string | undefined
         */
        to?: CategoryToEnumSchema;
        /**
         * @type string
         */
        from?: CategoryFromEnumSchema | null;
      };
      /**
       * @type object | undefined
       */
      closedAt?: IssueEventUpdateDateTimeChangeParametersSchema;
      /**
       * @type object | undefined
       */
      critical?: {
        /**
         * @type boolean
         */
        to?: boolean | null;
        /**
         * @type boolean
         */
        from?: boolean | null;
      };
      /**
       * @type object | undefined
       */
      delayFinish?: IssueEventUpdateDateTimeChangeParametersSchema;
      /**
       * @type object | undefined
       */
      delayStart?: IssueEventUpdateDateTimeChangeParametersSchema;
      /**
       * @type object | undefined
       */
      description?: IssueEventUpdateStringChangeParametersSchema;
      /**
       * @type object | undefined
       */
      discipline?: IssueEventUpdateStringChangeParametersSchema;
      /**
       * @type object | undefined
       */
      dueDate?: IssueEventUpdateDateTimeChangeParametersSchema;
      /**
       * @type object | undefined
       */
      immediateAction?: IssueEventUpdateStringChangeParametersSchema;
      /**
       * @type object | undefined
       */
      impact?: {
        to?: IssueImpactSchema | null;
        from?: IssueImpactSchema | null;
      };
      /**
       * @type object | undefined
       */
      location?: IssueEventUpdateStringChangeParametersSchema;
      /**
       * @type object | undefined
       */
      observedAt?: IssueEventUpdateDateTimeChangeParametersSchema;
      /**
       * @type object | undefined
       */
      peopleInvolvedSafety?: IssueEventUpdateStringChangeParametersSchema;
      /**
       * @type object | undefined
       */
      plannedClosureDate?: IssueEventUpdateDateTimeChangeParametersSchema;
      /**
       * @type object | undefined
       */
      potentialImpactSeverity?: IssueEventUpdateStringChangeParametersSchema;
      /**
       * @type object | undefined
       */
      preventativeAction?: IssueEventUpdateStringChangeParametersSchema;
      /**
       * @type object | undefined
       */
      safetyAlert?: {
        /**
         * @type boolean | undefined
         */
        to?: boolean;
        /**
         * @type boolean
         */
        from?: boolean | null;
      };
      /**
       * @type object | undefined
       */
      safetyLikelihoodScore?: {
        /**
         * @type integer
         */
        to?: number | null;
        /**
         * @type integer
         */
        from?: number | null;
      };
      /**
       * @type object | undefined
       */
      subCategory?: IssueEventUpdateStringChangeParametersSchema;
      /**
       * @type object | undefined
       */
      title?: IssueEventUpdateStringChangeParametersSchema;
      /**
       * @type object | undefined
       */
      visibilityStatus?: {
        to?: IssueImpactSchema | null;
        from?: IssueImpactSchema | null;
      };
      /**
       * @type object | undefined
       */
      workAffected?: IssueEventUpdateStringChangeParametersSchema;
    };
    [key: string]: unknown;
  };
};
