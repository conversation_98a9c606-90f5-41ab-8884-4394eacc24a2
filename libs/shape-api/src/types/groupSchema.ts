/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export type GroupSchema = {
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    delete: boolean;
    /**
     * @type boolean
     */
    edit: boolean;
  };
  /**
   * @type string
   */
  avatarUrl: string | null;
  /**
   * @type object
   */
  channels: {
    /**
     * @type string
     */
    channelCid: string;
  };
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  name: string | null;
};
