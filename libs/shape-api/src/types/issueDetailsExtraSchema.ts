/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueActivityLevelSchema } from './issueActivityLevelSchema';
import type { IssueApproverSchema } from './issueApproverSchema';
import type { IssueAssignmentSchema } from './issueAssignmentSchema';
import type { IssueCustomFieldListSchema } from './issueCustomFieldListSchema';
import type { IssueInvolvedTeamSchema } from './issueInvolvedTeamSchema';
import type { IssueStatusStatementSchema } from './issueStatusStatementSchema';
import type { UserBasicDetailsSchema } from './userBasicDetailsSchema';

export type IssueDetailsExtraSchema = {
  /**
   * @type array
   */
  customFields: IssueCustomFieldListSchema;
  /**
   * @type string, uuid
   */
  observerTeamId: string;
  /**
   * @type object
   */
  observerUser: UserBasicDetailsSchema;
  /**
   * @type string, uuid
   */
  assignedTeamId: string | null;
  /**
   * @type string
   */
  assignedTeamName: string | null;
  assignedUser: UserBasicDetailsSchema | null;
  issueAssignment: IssueAssignmentSchema | null;
  nextActionerUser: UserBasicDetailsSchema | null;
  /**
   * @type array
   */
  involvedTeams: IssueInvolvedTeamSchema[];
  /**
   * @type array
   */
  approvers: IssueApproverSchema[];
  activityLevel: IssueActivityLevelSchema | null;
  issueStatusStatement: IssueStatusStatementSchema | null;
  /**
   * @type boolean
   */
  isWatching: boolean;
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    acceptAssignment: boolean;
    /**
     * @type boolean
     */
    approve: boolean;
    /**
     * @type boolean
     */
    archive: boolean;
    /**
     * @type boolean
     */
    assign: boolean;
    /**
     * @type boolean
     */
    complete: boolean;
    /**
     * @type boolean
     */
    edit: boolean;
    /**
     * @type boolean
     */
    reject: boolean;
    /**
     * @type boolean
     */
    reopen: boolean;
    /**
     * @type boolean
     */
    restore: boolean;
    /**
     * @type boolean
     */
    start: boolean;
    /**
     * @type boolean
     */
    stop: boolean;
    /**
     * @type boolean
     */
    upload: boolean;
  };
};
