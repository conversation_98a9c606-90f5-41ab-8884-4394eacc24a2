/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ShiftActivityOverviewDocumentsSchema } from './shiftActivityOverviewDocumentsSchema';

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

export const getApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryParamsSortOrderEnum = {
  asc: 'asc',
  desc: 'desc',
} as const;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryParamsSortOrderEnumSchema =
  (typeof getApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryParamsSortOrderEnum)[keyof typeof getApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryParamsSortOrderEnum];

export const getApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryParamsKindEnum = {
  file: 'file',
  image: 'image',
} as const;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryParamsKindEnumSchema =
  (typeof getApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryParamsKindEnum)[keyof typeof getApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryParamsKindEnum];

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryParamsSchema = {
  /**
   * @description Order of the results
   * @type string | undefined
   */
  sort_order?: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryParamsSortOrderEnumSchema;
  /**
   * @type string | undefined
   */
  kind?: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryParamsKindEnumSchema;
  /**
   * @type string | undefined, uuid
   */
  weekly_work_plan_id?: string;
  /**
   * @type string | undefined, uuid
   */
  progress_log_id?: string;
  /**
   * @type string | undefined, uuid
   */
  shift_report_activity_id?: string;
};

/**
 * @description List of documents of an activity
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocuments200Schema =
  ShiftActivityOverviewDocumentsSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocuments401Schema = AuthenticationErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocuments404Schema = unknown;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryResponseSchema =
  GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocuments200Schema;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocuments200Schema;
  PathParams: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocumentsQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocuments401Schema
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDocuments404Schema;
};
