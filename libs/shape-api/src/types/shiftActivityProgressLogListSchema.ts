/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { CursorPaginationSchema } from './cursorPaginationSchema';
import type { ShiftActivityProgressLogBasicDetailsSchema } from './shiftActivityProgressLogBasicDetailsSchema';
import type { ShiftActivityProgressLogSchema } from './shiftActivityProgressLogSchema';

export type ShiftActivityProgressLogListSchema = {
  /**
   * @type array
   */
  entries: (ShiftActivityProgressLogBasicDetailsSchema | ShiftActivityProgressLogSchema)[];
} & CursorPaginationSchema;
