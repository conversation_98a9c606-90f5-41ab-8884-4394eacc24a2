/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ChangeSignalsBodyParameterSchema } from './changeSignalsBodyParameterSchema';
import type { ErrorSchema } from './errorSchema';
import type { PotentialChangeSchema } from './potentialChangeSchema';

export type PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  potential_change_id: string;
};

/**
 * @description Links change signals to potential changes
 */
export type PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinks200Schema =
  PotentialChangeSchema;

/**
 * @description Authentication required
 */
export type PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinks401Schema =
  AuthenticationErrorSchema;

/**
 * @description Unable to process request
 */
export type PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinks422Schema =
  ErrorSchema;

export type PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksMutationRequestSchema =
  ChangeSignalsBodyParameterSchema;

export type PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksMutationResponseSchema =
  PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinks200Schema;

export type PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksSchemaMutation = {
  Response: PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinks200Schema;
  Request: PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksMutationRequestSchema;
  PathParams: PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksPathParamsSchema;
  Errors:
    | PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinks401Schema
    | PutApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinks422Schema;
};
