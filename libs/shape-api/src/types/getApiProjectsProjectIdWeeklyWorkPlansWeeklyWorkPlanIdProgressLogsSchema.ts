/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ShiftActivityProgressLogListSchema } from './shiftActivityProgressLogListSchema';

export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogsQueryParamsSchema = {
  /**
   * @type string | undefined, date
   */
  date?: string;
};

/**
 * @description List all progress logs for activities linked to the weekly work plan
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogs200Schema =
  ShiftActivityProgressLogListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogs401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogs403Schema = unknown;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogs404Schema = unknown;

export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogsQueryResponseSchema =
  GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogs200Schema;

export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogsSchemaQuery = {
  Response: GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogs200Schema;
  PathParams: GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogsPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogsQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogs401Schema
    | GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogs403Schema
    | GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdProgressLogs404Schema;
};
