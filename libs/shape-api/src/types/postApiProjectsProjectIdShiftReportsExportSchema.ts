/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { OneOrManyIntegerSchema } from './oneOrManyIntegerSchema';
import type { QueuedTaskSchema } from './queuedTaskSchema';

export type PostApiProjectsProjectIdShiftReportsExportPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Request accepted
 */
export type PostApiProjectsProjectIdShiftReportsExport202Schema = QueuedTaskSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdShiftReportsExport400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftReportsExport401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftReportsExport403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftReportsExport404Schema = unknown;

export const postApiProjectsProjectIdShiftReportsExportMutationRequestFileTypeEnum = {
  pdf: 'pdf',
  xlsx: 'xlsx',
} as const;

export type PostApiProjectsProjectIdShiftReportsExportMutationRequestFileTypeEnumSchema =
  (typeof postApiProjectsProjectIdShiftReportsExportMutationRequestFileTypeEnum)[keyof typeof postApiProjectsProjectIdShiftReportsExportMutationRequestFileTypeEnum];

export type PostApiProjectsProjectIdShiftReportsExportMutationRequestSchema = {
  /**
   * @type string
   */
  file_type: PostApiProjectsProjectIdShiftReportsExportMutationRequestFileTypeEnumSchema;
  author_id?: OneOrManyIntegerSchema;
  /**
   * @type string | undefined, date
   */
  report_date_start?: string;
  /**
   * @type string | undefined, date
   */
  report_date_end?: string;
  /**
   * @type array | undefined
   */
  shift_report_ids?: string[];
};

export type PostApiProjectsProjectIdShiftReportsExportMutationResponseSchema =
  PostApiProjectsProjectIdShiftReportsExport202Schema;

export type PostApiProjectsProjectIdShiftReportsExportSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftReportsExport202Schema;
  Request: PostApiProjectsProjectIdShiftReportsExportMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftReportsExportPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftReportsExport400Schema
    | PostApiProjectsProjectIdShiftReportsExport401Schema
    | PostApiProjectsProjectIdShiftReportsExport403Schema
    | PostApiProjectsProjectIdShiftReportsExport404Schema;
};
