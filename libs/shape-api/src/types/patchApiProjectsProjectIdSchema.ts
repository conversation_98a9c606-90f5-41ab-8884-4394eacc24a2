/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ProjectSchema } from './projectSchema';

export type PatchApiProjectsProjectIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Project updated
 */
export type PatchApiProjectsProjectId200Schema = ProjectSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectId403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type PatchApiProjectsProjectId404Schema = unknown;

/**
 * @description Unprocessable entity
 */
export type PatchApiProjectsProjectId422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdMutationRequestSchema = {
  /**
   * @type object | undefined
   */
  project?: {
    /**
     * @type string | undefined
     */
    title?: string;
    /**
     * @type string | undefined
     */
    short_name?: string;
    /**
     * @description The signed id given by the direct upload method
     * @type string | undefined
     */
    logo?: string;
    /**
     * @type string | undefined
     */
    timezone?: string;
  };
};

export type PatchApiProjectsProjectIdMutationResponseSchema = PatchApiProjectsProjectId200Schema;

export type PatchApiProjectsProjectIdSchemaMutation = {
  Response: PatchApiProjectsProjectId200Schema;
  Request: PatchApiProjectsProjectIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectId401Schema
    | PatchApiProjectsProjectId403Schema
    | PatchApiProjectsProjectId404Schema
    | PatchApiProjectsProjectId422Schema;
};
