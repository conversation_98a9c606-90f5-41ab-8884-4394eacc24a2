/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersIssueCommentMentionTypeEnum = {
  issue_comment_mention: 'issue_comment_mention',
} as const;

export type NotificationParametersIssueCommentMentionTypeEnumSchema =
  (typeof notificationParametersIssueCommentMentionTypeEnum)[keyof typeof notificationParametersIssueCommentMentionTypeEnum];

export type NotificationParametersIssueCommentMentionSchema = {
  /**
   * @type string
   */
  type: NotificationParametersIssueCommentMentionTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    commentId: string;
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type boolean
     */
    privateComment: boolean;
    /**
     * @type string, uuid
     */
    projectId: string;
  };
};
