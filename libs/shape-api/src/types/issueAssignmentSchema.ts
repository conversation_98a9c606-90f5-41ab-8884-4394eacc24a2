/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueAssignmentStatusSchema } from './issueAssignmentStatusSchema';
import type { TeamMemberSchema } from './teamMemberSchema';

export type IssueAssignmentSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type object
   */
  assignee: TeamMemberSchema;
  /**
   * @type integer
   */
  assigneeId: number;
  /**
   * @type object
   */
  assigner: TeamMemberSchema;
  /**
   * @type integer
   */
  assignerId: number;
  /**
   * @type string
   */
  status: IssueAssignmentStatusSchema;
  /**
   * @type string
   */
  rejectReason: string | null;
  /**
   * @type boolean
   */
  canRespondTo: boolean;
};
