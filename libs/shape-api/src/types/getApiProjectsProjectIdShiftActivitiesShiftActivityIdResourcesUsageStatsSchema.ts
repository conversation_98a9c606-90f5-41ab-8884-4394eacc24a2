/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ShiftActivityOverviewResourcesUsageStatsSchema } from './shiftActivityOverviewResourcesUsageStatsSchema';

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStatsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description Stats of resources usage
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStats200Schema =
  ShiftActivityOverviewResourcesUsageStatsSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStats401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStats404Schema = unknown;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStatsQueryResponseSchema =
  GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStats200Schema;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStatsSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStats200Schema;
  PathParams: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStatsPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStats401Schema
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageStats404Schema;
};
