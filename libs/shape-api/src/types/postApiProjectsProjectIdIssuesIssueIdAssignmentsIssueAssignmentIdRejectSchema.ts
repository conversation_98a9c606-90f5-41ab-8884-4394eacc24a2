/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueAssignmentSchema } from './issueAssignmentSchema';

export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdRejectPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
  /**
   * @type string, uuid
   */
  issue_assignment_id: string;
};

/**
 * @description Issue assignment rejected
 */
export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdReject200Schema = IssueAssignmentSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdReject401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdReject403Schema = ErrorSchema;

/**
 * @description Issue assignment not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdReject404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdRejectMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  reject_reason?: string;
};

export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdRejectMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdReject200Schema;

export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdRejectSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdReject200Schema;
  Request: PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdRejectMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdRejectPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdReject401Schema
    | PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdReject403Schema
    | PostApiProjectsProjectIdIssuesIssueIdAssignmentsIssueAssignmentIdReject404Schema;
};
