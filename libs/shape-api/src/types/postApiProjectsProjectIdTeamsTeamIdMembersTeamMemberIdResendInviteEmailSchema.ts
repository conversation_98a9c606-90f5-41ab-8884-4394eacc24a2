/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmailPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
  /**
   * @type integer
   */
  team_member_id: number;
};

/**
 * @description Invite sent
 */
export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmail202Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmail401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmail403Schema = ErrorSchema;

/**
 * @description Team member not found
 */
export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmail404Schema = unknown;

export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmailMutationResponseSchema =
  PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmail202Schema;

export type PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmailSchemaMutation = {
  Response: PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmail202Schema;
  PathParams: PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmailPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmail401Schema
    | PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmail403Schema
    | PostApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdResendInviteEmail404Schema;
};
