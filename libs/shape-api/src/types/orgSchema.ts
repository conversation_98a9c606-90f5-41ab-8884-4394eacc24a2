/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export type OrgSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  name: string;
  /**
   * @type string
   */
  shortName: string;
  /**
   * @type boolean
   */
  verified: boolean;
  /**
   * @type string
   */
  role: string | null;
  /**
   * @type boolean
   */
  logoUrl: boolean | null;
  /**
   * @type string
   */
  verificationEmail?: string | null;
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    createProject: boolean;
  };
};
