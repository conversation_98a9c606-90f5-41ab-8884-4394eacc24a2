/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ProjectAccessRequestSchema } from './projectAccessRequestSchema';

export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRejectPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  project_access_request_id: string;
};

/**
 * @description Project access request rejected
 */
export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdReject200Schema = ProjectAccessRequestSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdReject401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdReject403Schema = ErrorSchema;

/**
 * @description Request not found
 */
export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdReject404Schema = unknown;

export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRejectMutationResponseSchema =
  PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdReject200Schema;

export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRejectSchemaMutation = {
  Response: PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdReject200Schema;
  PathParams: PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRejectPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdReject401Schema
    | PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdReject403Schema
    | PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdReject404Schema;
};
