/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DataHealthRecordsShiftReportListSchema } from './dataHealthRecordsShiftReportListSchema';
import type { ErrorSchema } from './errorSchema';

export type GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsQueryParamsSchema = {
  /**
   * @type string | undefined, date
   */
  date_start?: string;
  /**
   * @type string | undefined, date
   */
  date_end?: string;
  /**
   * @type integer | undefined
   */
  team_member_id?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
};

/**
 * @description List of data health records
 */
export type GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReports200Schema =
  DataHealthRecordsShiftReportListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReports401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReports403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReports404Schema = unknown;

export type GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsQueryResponseSchema =
  GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReports200Schema;

export type GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsSchemaQuery = {
  Response: GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReports200Schema;
  PathParams: GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReportsQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReports401Schema
    | GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReports403Schema
    | GetApiProjectsProjectIdDashboardsDataHealthRecordsShiftReports404Schema;
};
