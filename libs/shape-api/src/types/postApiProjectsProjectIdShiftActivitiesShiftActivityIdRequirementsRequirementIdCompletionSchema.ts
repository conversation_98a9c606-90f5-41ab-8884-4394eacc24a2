/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftActivityRequirementSchema } from './shiftActivityRequirementSchema';

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionPathParamsSchema =
  {
    /**
     * @type string, uuid
     */
    project_id: string;
    /**
     * @type string, uuid
     */
    shift_activity_id: string;
    /**
     * @type string, uuid
     */
    requirement_id: string;
  };

/**
 * @description Shift activity requirement marked as completed
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletion200Schema =
  ShiftActivityRequirementSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletion401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletion403Schema =
  ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletion404Schema =
  unknown;

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionMutationResponseSchema =
  PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletion200Schema;

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletion200Schema;
  PathParams: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletionPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletion401Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletion403Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdCompletion404Schema;
};
