/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ProjectAccessRequestSchema } from './projectAccessRequestSchema';

export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirectPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  project_access_request_id: string;
};

/**
 * @description Project access request redirected
 */
export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirect200Schema = ProjectAccessRequestSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirect400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirect401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirect403Schema = ErrorSchema;

/**
 * @description Request not found
 */
export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirect404Schema = unknown;

export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirectMutationRequestSchema = {
  /**
   * @type string, uuid
   */
  team_id: string;
};

export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirectMutationResponseSchema =
  PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirect200Schema;

export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirectSchemaMutation = {
  Response: PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirect200Schema;
  Request: PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirectMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirectPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirect400Schema
    | PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirect401Schema
    | PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirect403Schema
    | PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdRedirect404Schema;
};
