/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { OneOrManyUuidSchema } from './oneOrManyUuidSchema';

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatchPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description Shift activity blockers created
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatch204Schema = unknown;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatch400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatch401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatch403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatch404Schema = unknown;

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatchMutationRequestSchema = {
  issue_ids: OneOrManyUuidSchema;
};

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatchMutationResponseSchema =
  PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatch204Schema;

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatchSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatch204Schema;
  Request: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatchMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatchPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatch400Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatch401Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatch403Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersBatch404Schema;
};
