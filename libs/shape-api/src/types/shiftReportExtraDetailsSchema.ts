/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { ShiftReportActivitySchema } from './shiftReportActivitySchema';
import type { ShiftReportContractForceSchema } from './shiftReportContractForceSchema';
import type { ShiftReportDownTimeSchema } from './shiftReportDownTimeSchema';
import type { ShiftReportEquipmentSchema } from './shiftReportEquipmentSchema';
import type { ShiftReportMaterialSchema } from './shiftReportMaterialSchema';
import type { ShiftReportSafetyHealthEnvironmentSchema } from './shiftReportSafetyHealthEnvironmentSchema';
import type { ShiftReportVisibilitySchema } from './shiftReportVisibilitySchema';

export type ShiftReportExtraDetailsSchema = {
  /**
   * @type array
   */
  activities: ShiftReportActivitySchema[];
  /**
   * @type string
   */
  clientDocumentReferenceNumber: string | null;
  /**
   * @type array
   */
  contractForces: ShiftReportContractForceSchema[];
  /**
   * @type object
   */
  contractorLogo?: {
    /**
     * @type string
     */
    signedId: string;
    /**
     * @type string
     */
    url: string;
  } | null;
  /**
   * @type array
   */
  downTimes: ShiftReportDownTimeSchema[];
  /**
   * @type array
   */
  equipments: ShiftReportEquipmentSchema[];
  /**
   * @type string
   */
  internalDocumentReferenceNumber: string | null;
  /**
   * @type array
   */
  materials: ShiftReportMaterialSchema[];
  /**
   * @type string
   */
  notes: string | null;
  /**
   * @type string
   */
  projectNumber: string | null;
  /**
   * @type array
   */
  safetyHealthEnvironments: ShiftReportSafetyHealthEnvironmentSchema[];
  /**
   * @type string, time
   */
  shiftEnd: string | null;
  /**
   * @type string, time
   */
  shiftStart: string | null;
  /**
   * @type string
   */
  visibility: ShiftReportVisibilitySchema;
  /**
   * @type array
   */
  visibilitySpecificTeamIds: string[];
  /**
   * @type string
   */
  weatherDescription: string | null;
  /**
   * @type string
   */
  weatherTemperature: string | null;
};
