/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { FeedbackSchema } from './feedbackSchema';

/**
 * @description Feedback created
 */
export type PostApiFeedbacks201Schema = FeedbackSchema;

/**
 * @description Authentication required
 */
export type PostApiFeedbacks401Schema = AuthenticationErrorSchema;

/**
 * @description Invalid request
 */
export type PostApiFeedbacks422Schema = ErrorSchema;

export type PostApiFeedbacksMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  like?: string;
  /**
   * @type string | undefined
   */
  improvement?: string;
  /**
   * @type string | undefined
   */
  app_revision?: string;
  /**
   * @type string | undefined
   */
  app_version?: string;
};

export type PostApiFeedbacksMutationResponseSchema = PostApiFeedbacks201Schema;

export type PostApiFeedbacksSchemaMutation = {
  Response: PostApiFeedbacks201Schema;
  Request: PostApiFeedbacksMutationRequestSchema;
  Errors: PostApiFeedbacks401Schema | PostApiFeedbacks422Schema;
};
