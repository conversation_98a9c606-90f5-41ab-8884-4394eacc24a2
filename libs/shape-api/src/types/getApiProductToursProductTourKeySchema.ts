/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { UserProductTourSchema } from './userProductTourSchema';

export type GetApiProductToursProductTourKeyPathParamsSchema = {
  /**
   * @type string
   */
  product_tour_key: string;
};

/**
 * @description User product tour
 */
export type GetApiProductToursProductTourKey200Schema = UserProductTourSchema;

/**
 * @description Authentication required
 */
export type GetApiProductToursProductTourKey401Schema = AuthenticationErrorSchema;

/**
 * @description Product tour not found
 */
export type GetApiProductToursProductTourKey404Schema = unknown;

export type GetApiProductToursProductTourKeyQueryResponseSchema = GetApiProductToursProductTourKey200Schema;

export type GetApiProductToursProductTourKeySchemaQuery = {
  Response: GetApiProductToursProductTourKey200Schema;
  PathParams: GetApiProductToursProductTourKeyPathParamsSchema;
  Errors: GetApiProductToursProductTourKey401Schema | GetApiProductToursProductTourKey404Schema;
};
