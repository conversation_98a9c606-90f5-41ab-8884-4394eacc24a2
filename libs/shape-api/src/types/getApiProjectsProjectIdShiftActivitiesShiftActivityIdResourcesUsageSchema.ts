/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ShiftActivityOverviewResourcesUsageListSchema } from './shiftActivityOverviewResourcesUsageListSchema';

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsagePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description List of material usage
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsage200Schema =
  ShiftActivityOverviewResourcesUsageListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsage401Schema = AuthenticationErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsage404Schema = unknown;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageQueryResponseSchema =
  GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsage200Schema;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsageSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsage200Schema;
  PathParams: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsagePathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsage401Schema
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdResourcesUsage404Schema;
};
