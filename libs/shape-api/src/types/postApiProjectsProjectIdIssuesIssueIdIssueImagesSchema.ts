/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueImageKindSchema } from './issueImageKindSchema';
import type { IssueImageSchema } from './issueImageSchema';

export type PostApiProjectsProjectIdIssuesIssueIdIssueImagesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Image uploaded
 * @deprecated
 */
export type PostApiProjectsProjectIdIssuesIssueIdIssueImages201Schema = IssueImageSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdIssuesIssueIdIssueImages400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdIssueImages401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesIssueIdIssueImages403Schema = ErrorSchema;

/**
 * @description Invalid image
 */
export type PostApiProjectsProjectIdIssuesIssueIdIssueImages422Schema = ErrorSchema;

export type PostApiProjectsProjectIdIssuesIssueIdIssueImagesMutationRequestSchema = {
  /**
   * @description The id of an existing image document
   * @type string | undefined, uuid
   */
  document_id?: string;
  /**
   * @description The signed id given by the direct upload method
   * @type string | undefined
   */
  file_signed_id?: string;
  /**
   * @type string | undefined
   */
  kind?: IssueImageKindSchema;
};

export type PostApiProjectsProjectIdIssuesIssueIdIssueImagesMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdIssueImages201Schema;

export type PostApiProjectsProjectIdIssuesIssueIdIssueImagesSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdIssueImages201Schema;
  Request: PostApiProjectsProjectIdIssuesIssueIdIssueImagesMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdIssueImagesPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdIssueImages400Schema
    | PostApiProjectsProjectIdIssuesIssueIdIssueImages401Schema
    | PostApiProjectsProjectIdIssuesIssueIdIssueImages403Schema
    | PostApiProjectsProjectIdIssuesIssueIdIssueImages422Schema;
};
