/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueEventTypeSchema } from './issueEventTypeSchema';
import type { ProjectIssueEventListSchema } from './projectIssueEventListSchema';

export type GetApiProjectsProjectIdEventsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type GetApiProjectsProjectIdEventsQueryParamsSchema = {
  event_type?: IssueEventTypeSchema[] | IssueEventTypeSchema;
  /**
   * @description Filter by elements created before (not including) the date time.
   * @type string | undefined, date-time
   */
  ending_before?: string;
  /**
   * @description Filter by elements created after (not including) the date time.
   * @type string | undefined, date-time
   */
  starting_after?: string;
};

/**
 * @description List of events
 */
export type GetApiProjectsProjectIdEvents200Schema = ProjectIssueEventListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdEvents401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdEvents403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type GetApiProjectsProjectIdEvents404Schema = unknown;

export type GetApiProjectsProjectIdEventsQueryResponseSchema = GetApiProjectsProjectIdEvents200Schema;

export type GetApiProjectsProjectIdEventsSchemaQuery = {
  Response: GetApiProjectsProjectIdEvents200Schema;
  PathParams: GetApiProjectsProjectIdEventsPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdEventsQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdEvents401Schema
    | GetApiProjectsProjectIdEvents403Schema
    | GetApiProjectsProjectIdEvents404Schema;
};
