/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersYourIssueAssignmentWasRejectedTypeEnum = {
  your_issue_assignment_was_rejected: 'your_issue_assignment_was_rejected',
} as const;

export type NotificationParametersYourIssueAssignmentWasRejectedTypeEnumSchema =
  (typeof notificationParametersYourIssueAssignmentWasRejectedTypeEnum)[keyof typeof notificationParametersYourIssueAssignmentWasRejectedTypeEnum];

export type NotificationParametersYourIssueAssignmentWasRejectedSchema = {
  /**
   * @type string
   */
  type: NotificationParametersYourIssueAssignmentWasRejectedTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type string, uuid
     */
    projectId: string;
  };
};
