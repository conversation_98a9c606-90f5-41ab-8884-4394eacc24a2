/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export const notificationTypeEnum = {
  added_to_project: 'added_to_project',
  issue_comment_mention: 'issue_comment_mention',
  issue_needs_your_approval: 'issue_needs_your_approval',
  issue_was_reopened: 'issue_was_reopened',
  issue_was_resolved: 'issue_was_resolved',
  new_issue_comment: 'new_issue_comment',
  new_issue_private_comment: 'new_issue_private_comment',
  new_issue_status_statement: 'new_issue_status_statement',
  new_shift_report_comment: 'new_shift_report_comment',
  shift_report_comment_mention: 'shift_report_comment_mention',
  you_were_assigned_to_issue: 'you_were_assigned_to_issue',
  your_issue_approval_request_was_accepted: 'your_issue_approval_request_was_accepted',
  your_issue_approval_request_was_rejected: 'your_issue_approval_request_was_rejected',
  your_issue_assignment_was_accepted: 'your_issue_assignment_was_accepted',
  your_issue_assignment_was_rejected: 'your_issue_assignment_was_rejected',
  your_issue_was_reassigned: 'your_issue_was_reassigned',
} as const;

export type NotificationTypeEnumSchema = (typeof notificationTypeEnum)[keyof typeof notificationTypeEnum];

export type NotificationTypeSchema = NotificationTypeEnumSchema;
