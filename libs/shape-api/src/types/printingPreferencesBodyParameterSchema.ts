/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { PrintingPreferencesDisplaySchema } from './printingPreferencesDisplaySchema';

export const issueDetailsItemsEnum = {
  closed_at: 'closed_at',
  created_at: 'created_at',
  description: 'description',
  discipline: 'discipline',
  due_date: 'due_date',
  location: 'location',
  observed_at: 'observed_at',
  state: 'state',
  status_statement: 'status_statement',
  title: 'title',
  type: 'type',
} as const;

export type IssueDetailsItemsEnumSchema = (typeof issueDetailsItemsEnum)[keyof typeof issueDetailsItemsEnum];

export const impactItemsEnum = {
  delay_finish: 'delay_finish',
  delay_start: 'delay_start',
  due_date: 'due_date',
  impact: 'impact',
  work_affected: 'work_affected',
} as const;

export type ImpactItemsEnumSchema = (typeof impactItemsEnum)[keyof typeof impactItemsEnum];

export const peopleTeamsItemsEnum = {
  responsible_person: 'responsible_person',
  team: 'team',
  observer: 'observer',
} as const;

export type PeopleTeamsItemsEnumSchema = (typeof peopleTeamsItemsEnum)[keyof typeof peopleTeamsItemsEnum];

export const visibilityItemsEnum = {
  visibility: 'visibility',
} as const;

export type VisibilityItemsEnumSchema = (typeof visibilityItemsEnum)[keyof typeof visibilityItemsEnum];

export type PrintingPreferencesBodyParameterSchema = {
  /**
   * @type object | undefined
   */
  printing_preferences?: {
    /**
     * @type object | undefined
     */
    approvers?: {
      /**
       * @type string | undefined
       */
      display?: PrintingPreferencesDisplaySchema;
      /**
       * @type array | undefined
       */
      items?: string[];
    };
    /**
     * @type object | undefined
     */
    custom_fields?: {
      /**
       * @type string | undefined
       */
      display?: PrintingPreferencesDisplaySchema;
      /**
       * @type array | undefined
       */
      items?: string[];
    };
    /**
     * @type object | undefined
     */
    issue_details?: {
      /**
       * @type string | undefined
       */
      display?: PrintingPreferencesDisplaySchema;
      /**
       * @type array | undefined
       */
      items?: IssueDetailsItemsEnumSchema[];
    };
    /**
     * @type object | undefined
     */
    documents?: {
      /**
       * @type string | undefined
       */
      display?: PrintingPreferencesDisplaySchema;
      /**
       * @type array | undefined
       */
      items?: string[];
    };
    /**
     * @type object | undefined
     */
    impact?: {
      /**
       * @type string | undefined
       */
      display?: PrintingPreferencesDisplaySchema;
      /**
       * @type array | undefined
       */
      items?: ImpactItemsEnumSchema[];
    };
    /**
     * @type object | undefined
     */
    people_teams?: {
      /**
       * @type string | undefined
       */
      display?: PrintingPreferencesDisplaySchema;
      /**
       * @type array | undefined
       */
      items?: PeopleTeamsItemsEnumSchema[];
    };
    /**
     * @type object | undefined
     */
    status_statements?: {
      /**
       * @type string | undefined
       */
      display?: PrintingPreferencesDisplaySchema;
      /**
       * @type array | undefined
       */
      items?: string[];
    };
    /**
     * @type object | undefined
     */
    updates?: {
      /**
       * @type string | undefined
       */
      display?: PrintingPreferencesDisplaySchema;
      /**
       * @type array | undefined
       */
      items?: string[];
    };
    /**
     * @type object | undefined
     */
    visibility?: {
      /**
       * @type string | undefined
       */
      display?: PrintingPreferencesDisplaySchema;
      /**
       * @type array | undefined
       */
      items?: VisibilityItemsEnumSchema[];
    };
  };
};
