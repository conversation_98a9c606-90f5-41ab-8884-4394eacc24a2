/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DocumentSchema } from './documentSchema';
import type { ErrorSchema } from './errorSchema';

export type GetApiProjectsProjectIdDocumentsDocumentIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  document_id: string;
};

/**
 * @description Document
 */
export type GetApiProjectsProjectIdDocumentsDocumentId200Schema = DocumentSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdDocumentsDocumentId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdDocumentsDocumentId403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdDocumentsDocumentId404Schema = unknown;

export type GetApiProjectsProjectIdDocumentsDocumentIdQueryResponseSchema =
  GetApiProjectsProjectIdDocumentsDocumentId200Schema;

export type GetApiProjectsProjectIdDocumentsDocumentIdSchemaQuery = {
  Response: GetApiProjectsProjectIdDocumentsDocumentId200Schema;
  PathParams: GetApiProjectsProjectIdDocumentsDocumentIdPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdDocumentsDocumentId401Schema
    | GetApiProjectsProjectIdDocumentsDocumentId403Schema
    | GetApiProjectsProjectIdDocumentsDocumentId404Schema;
};
