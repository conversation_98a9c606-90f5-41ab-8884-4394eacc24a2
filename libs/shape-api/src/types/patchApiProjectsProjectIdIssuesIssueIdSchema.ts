/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueCategorySchema } from './issueCategorySchema';
import type { IssueSchema } from './issueSchema';
import type { IssueVisibilityStatusSchema } from './issueVisibilityStatusSchema';

export type PatchApiProjectsProjectIdIssuesIssueIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue updated
 */
export type PatchApiProjectsProjectIdIssuesIssueId200Schema = IssueSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdIssuesIssueId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdIssuesIssueId403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type PatchApiProjectsProjectIdIssuesIssueId404Schema = unknown;

/**
 * @description Unable to update
 */
export type PatchApiProjectsProjectIdIssuesIssueId422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdIssuesIssueIdMutationRequestSchema = {
  /**
   * @type object | undefined
   */
  issue?: {
    /**
     * @type array
     */
    approvers_attributes?:
      | {
          /**
           * @type string | undefined, uuid
           */
          id?: string;
          /**
           * @type integer | undefined
           */
          team_member_id?: number;
          /**
           * @type integer | undefined
           */
          sort_order?: number;
          /**
           * @type integer | undefined
           */
          _destroy?: number;
        }[]
      | null;
    category?: IssueCategorySchema | null;
    /**
     * @type string, date-time
     */
    closed_at?: string | null;
    /**
     * @type boolean | undefined
     */
    critical?: boolean;
    /**
     * @type string
     */
    description?: string | null;
    /**
     * @type string, uuid
     */
    discipline_id?: string | null;
    /**
     * @type integer
     */
    draft_assignee_id?: number | null;
    /**
     * @type string, date-time
     */
    due_date?: string | null;
    /**
     * @type string
     */
    immediate_action?: string | null;
    /**
     * @type array
     */
    involved_teams_attributes?:
      | {
          /**
           * @type string | undefined, uuid
           */
          id?: string;
          /**
           * @type string | undefined
           */
          role?: string;
          /**
           * @type string | undefined, uuid
           */
          team_id?: string;
          /**
           * @type integer | undefined
           */
          _destroy?: number;
        }[]
      | null;
    /**
     * @type string, uuid
     */
    location_id?: string | null;
    /**
     * @type string | undefined, date-time
     */
    observed_at?: string;
    /**
     * @type string
     */
    people_involved_safety?: string | null;
    /**
     * @type string, date-time
     */
    planned_closure_date?: string | null;
    /**
     * @type string
     */
    potential_impact_severity?: string | null;
    /**
     * @type string
     */
    preventative_action?: string | null;
    /**
     * @type boolean | undefined
     */
    safety_alert?: boolean;
    /**
     * @type integer
     */
    safety_likelihood_score?: number | null;
    /**
     * @type string
     */
    sub_category?: string | null;
    /**
     * @type string
     */
    title?: string | null;
    visibility_status?: IssueVisibilityStatusSchema | null;
  };
};

export type PatchApiProjectsProjectIdIssuesIssueIdMutationResponseSchema =
  PatchApiProjectsProjectIdIssuesIssueId200Schema;

export type PatchApiProjectsProjectIdIssuesIssueIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdIssuesIssueId200Schema;
  Request: PatchApiProjectsProjectIdIssuesIssueIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdIssuesIssueIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdIssuesIssueId401Schema
    | PatchApiProjectsProjectIdIssuesIssueId403Schema
    | PatchApiProjectsProjectIdIssuesIssueId404Schema
    | PatchApiProjectsProjectIdIssuesIssueId422Schema;
};
