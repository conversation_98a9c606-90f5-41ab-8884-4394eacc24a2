/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ResourceKindSchema } from './resourceKindSchema';
import type { ResourceListSchema } from './resourceListSchema';

export type GetApiProjectsProjectIdTeamsTeamIdResourcesKindPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
  /**
   * @type string
   */
  kind: ResourceKindSchema;
};

export type GetApiProjectsProjectIdTeamsTeamIdResourcesKindQueryParamsSchema = {
  /**
   * @type string | undefined
   */
  search?: string;
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description Resources
 */
export type GetApiProjectsProjectIdTeamsTeamIdResourcesKind200Schema = ResourceListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdTeamsTeamIdResourcesKind401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type GetApiProjectsProjectIdTeamsTeamIdResourcesKind403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdTeamsTeamIdResourcesKind404Schema = unknown;

export type GetApiProjectsProjectIdTeamsTeamIdResourcesKindQueryResponseSchema =
  GetApiProjectsProjectIdTeamsTeamIdResourcesKind200Schema;

export type GetApiProjectsProjectIdTeamsTeamIdResourcesKindSchemaQuery = {
  Response: GetApiProjectsProjectIdTeamsTeamIdResourcesKind200Schema;
  PathParams: GetApiProjectsProjectIdTeamsTeamIdResourcesKindPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdTeamsTeamIdResourcesKindQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdTeamsTeamIdResourcesKind401Schema
    | GetApiProjectsProjectIdTeamsTeamIdResourcesKind403Schema
    | GetApiProjectsProjectIdTeamsTeamIdResourcesKind404Schema;
};
