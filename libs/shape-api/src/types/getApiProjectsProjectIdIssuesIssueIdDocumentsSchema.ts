/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DocumentKindSchema } from './documentKindSchema';
import type { DocumentReferenceAndDocumentListSchema } from './documentReferenceAndDocumentListSchema';
import type { ErrorSchema } from './errorSchema';
import type { OneOrManyIntegerSchema } from './oneOrManyIntegerSchema';
import type { OneOrManyUuidNullableSchema } from './oneOrManyUuidNullableSchema';

export type GetApiProjectsProjectIdIssuesIssueIdDocumentsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

export const getApiProjectsProjectIdIssuesIssueIdDocumentsQueryParamsSortOrderEnum = {
  asc: 'asc',
  desc: 'desc',
} as const;

export type GetApiProjectsProjectIdIssuesIssueIdDocumentsQueryParamsSortOrderEnumSchema =
  (typeof getApiProjectsProjectIdIssuesIssueIdDocumentsQueryParamsSortOrderEnum)[keyof typeof getApiProjectsProjectIdIssuesIssueIdDocumentsQueryParamsSortOrderEnum];

export type GetApiProjectsProjectIdIssuesIssueIdDocumentsQueryParamsSchema = {
  /**
   * @type boolean | undefined
   */
  completion_evidence?: boolean;
  /**
   * @type string | undefined, date-time
   */
  created_end?: string;
  /**
   * @type string | undefined, date-time
   */
  created_start?: string;
  /**
   * @type string | undefined
   */
  kind?: DocumentKindSchema;
  location_id?: OneOrManyUuidNullableSchema;
  team_member_id?: OneOrManyIntegerSchema;
  /**
   * @description Generic search query
   * @type string | undefined
   */
  search?: string;
  /**
   * @description Order of the results
   * @type string | undefined
   */
  sort_order?: GetApiProjectsProjectIdIssuesIssueIdDocumentsQueryParamsSortOrderEnumSchema;
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of issue documents
 */
export type GetApiProjectsProjectIdIssuesIssueIdDocuments200Schema = DocumentReferenceAndDocumentListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdIssuesIssueIdDocuments400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdIssuesIssueIdDocuments401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdIssuesIssueIdDocuments403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdIssuesIssueIdDocuments404Schema = unknown;

export type GetApiProjectsProjectIdIssuesIssueIdDocumentsQueryResponseSchema =
  GetApiProjectsProjectIdIssuesIssueIdDocuments200Schema;

export type GetApiProjectsProjectIdIssuesIssueIdDocumentsSchemaQuery = {
  Response: GetApiProjectsProjectIdIssuesIssueIdDocuments200Schema;
  PathParams: GetApiProjectsProjectIdIssuesIssueIdDocumentsPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdIssuesIssueIdDocumentsQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdIssuesIssueIdDocuments400Schema
    | GetApiProjectsProjectIdIssuesIssueIdDocuments401Schema
    | GetApiProjectsProjectIdIssuesIssueIdDocuments403Schema
    | GetApiProjectsProjectIdIssuesIssueIdDocuments404Schema;
};
