/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DocumentSchema } from './documentSchema';
import type { ErrorSchema } from './errorSchema';

export type PatchApiProjectsProjectIdDocumentsDocumentIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  document_id: string;
};

/**
 * @description Document updated
 */
export type PatchApiProjectsProjectIdDocumentsDocumentId200Schema = DocumentSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdDocumentsDocumentId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdDocumentsDocumentId403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PatchApiProjectsProjectIdDocumentsDocumentId404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdDocumentsDocumentId422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdDocumentsDocumentIdMutationRequestSchema = {
  /**
   * @type string
   */
  caption?: string | null;
  /**
   * @type string, uuid
   */
  location_id?: string | null;
};

export type PatchApiProjectsProjectIdDocumentsDocumentIdMutationResponseSchema =
  PatchApiProjectsProjectIdDocumentsDocumentId200Schema;

export type PatchApiProjectsProjectIdDocumentsDocumentIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdDocumentsDocumentId200Schema;
  Request: PatchApiProjectsProjectIdDocumentsDocumentIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdDocumentsDocumentIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdDocumentsDocumentId401Schema
    | PatchApiProjectsProjectIdDocumentsDocumentId403Schema
    | PatchApiProjectsProjectIdDocumentsDocumentId404Schema
    | PatchApiProjectsProjectIdDocumentsDocumentId422Schema;
};
