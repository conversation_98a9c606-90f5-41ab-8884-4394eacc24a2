/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { ResetPasswordErrorSchema } from './resetPasswordErrorSchema';

/**
 * @description Password updated
 */
export type PatchApiUsersPassword204Schema = unknown;

/**
 * @description Invalid token
 */
export type PatchApiUsersPassword400Schema = ResetPasswordErrorSchema;

/**
 * @description Reset password failed
 */
export type PatchApiUsersPassword422Schema = ResetPasswordErrorSchema;

export type PatchApiUsersPasswordMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  email?: string;
  /**
   * @type string
   */
  password: string;
  /**
   * @type string
   */
  password_confirmation: string;
  /**
   * @type string
   */
  reset_password_token: string;
};

export type PatchApiUsersPasswordMutationResponseSchema = PatchApiUsersPassword204Schema;

export type PatchApiUsersPasswordSchemaMutation = {
  Response: PatchApiUsersPassword204Schema;
  Request: PatchApiUsersPasswordMutationRequestSchema;
  Errors: PatchApiUsersPassword400Schema | PatchApiUsersPassword422Schema;
};
