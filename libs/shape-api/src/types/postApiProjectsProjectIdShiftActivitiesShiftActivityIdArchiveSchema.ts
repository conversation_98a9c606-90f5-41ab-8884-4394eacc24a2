/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftActivitySchema } from './shiftActivitySchema';

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchivePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description Shift activity archived
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchive200Schema = ShiftActivitySchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchive401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchive403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchive404Schema = unknown;

/**
 * @description Archive failed
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchive422Schema = ErrorSchema;

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchiveMutationResponseSchema =
  PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchive200Schema;

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchiveSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchive200Schema;
  PathParams: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchivePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchive401Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchive403Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchive404Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdArchive422Schema;
};
