/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DocumentKindSchema } from './documentKindSchema';
import type { DocumentListSchema } from './documentListSchema';
import type { ErrorSchema } from './errorSchema';
import type { OneOrManyIntegerSchema } from './oneOrManyIntegerSchema';
import type { OneOrManyUuidNullableSchema } from './oneOrManyUuidNullableSchema';

export type GetApiProjectsProjectIdDocumentsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export const getApiProjectsProjectIdDocumentsQueryParamsSortOrderEnum = {
  asc: 'asc',
  desc: 'desc',
} as const;

export type GetApiProjectsProjectIdDocumentsQueryParamsSortOrderEnumSchema =
  (typeof getApiProjectsProjectIdDocumentsQueryParamsSortOrderEnum)[keyof typeof getApiProjectsProjectIdDocumentsQueryParamsSortOrderEnum];

export type GetApiProjectsProjectIdDocumentsQueryParamsSchema = {
  /**
   * @type string | undefined
   */
  kind?: DocumentKindSchema;
  location_id?: OneOrManyUuidNullableSchema;
  team_member_id?: OneOrManyIntegerSchema;
  /**
   * @type string | undefined, date-time
   */
  created_start?: string;
  /**
   * @type string | undefined, date-time
   */
  created_end?: string;
  /**
   * @description Generic search query
   * @type string | undefined
   */
  search?: string;
  /**
   * @description Order of the results
   * @type string | undefined
   */
  sort_order?: GetApiProjectsProjectIdDocumentsQueryParamsSortOrderEnumSchema;
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of documents
 */
export type GetApiProjectsProjectIdDocuments200Schema = DocumentListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdDocuments400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdDocuments401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdDocuments403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdDocuments404Schema = unknown;

export type GetApiProjectsProjectIdDocumentsQueryResponseSchema = GetApiProjectsProjectIdDocuments200Schema;

export type GetApiProjectsProjectIdDocumentsSchemaQuery = {
  Response: GetApiProjectsProjectIdDocuments200Schema;
  PathParams: GetApiProjectsProjectIdDocumentsPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdDocumentsQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdDocuments400Schema
    | GetApiProjectsProjectIdDocuments401Schema
    | GetApiProjectsProjectIdDocuments403Schema
    | GetApiProjectsProjectIdDocuments404Schema;
};
