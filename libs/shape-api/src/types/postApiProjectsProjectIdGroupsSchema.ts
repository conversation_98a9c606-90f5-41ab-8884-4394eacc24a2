/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { GroupSchema } from './groupSchema';

export type PostApiProjectsProjectIdGroupsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Group created
 */
export type PostApiProjectsProjectIdGroups201Schema = GroupSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdGroups400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdGroups401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdGroups403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type PostApiProjectsProjectIdGroups404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdGroups422Schema = ErrorSchema;

export type PostApiProjectsProjectIdGroupsMutationRequestSchema = {
  /**
   * @type string
   */
  name?: string | null;
  /**
   * @description The signed id given by the direct upload method
   * @type string
   */
  avatar?: string | null;
  /**
   * @type array | undefined
   */
  user_ids?: string[];
};

export type PostApiProjectsProjectIdGroupsMutationResponseSchema = PostApiProjectsProjectIdGroups201Schema;

export type PostApiProjectsProjectIdGroupsSchemaMutation = {
  Response: PostApiProjectsProjectIdGroups201Schema;
  Request: PostApiProjectsProjectIdGroupsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdGroupsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdGroups400Schema
    | PostApiProjectsProjectIdGroups401Schema
    | PostApiProjectsProjectIdGroups403Schema
    | PostApiProjectsProjectIdGroups404Schema
    | PostApiProjectsProjectIdGroups422Schema;
};
