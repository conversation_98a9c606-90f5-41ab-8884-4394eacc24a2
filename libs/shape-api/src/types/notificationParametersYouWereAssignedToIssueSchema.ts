/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export type NotificationParametersYouWereAssignedToIssueSchema = {
  /**
   * @type string
   */
  type: string;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type string, uuid
     */
    projectId: string;
  };
};
