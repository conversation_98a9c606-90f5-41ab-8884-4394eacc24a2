/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersYouWereAssignedToIssueTypeEnum = {
  you_were_assigned_to_issue: 'you_were_assigned_to_issue',
} as const;

export type NotificationParametersYouWereAssignedToIssueTypeEnumSchema =
  (typeof notificationParametersYouWereAssignedToIssueTypeEnum)[keyof typeof notificationParametersYouWereAssignedToIssueTypeEnum];

export type NotificationParametersYouWereAssignedToIssueSchema = {
  /**
   * @type string
   */
  type: NotificationParametersYouWereAssignedToIssueTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type string, uuid
     */
    projectId: string;
  };
};
