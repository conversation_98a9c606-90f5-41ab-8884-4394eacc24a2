/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { TeamMemberIssueDependencySchema } from './teamMemberIssueDependencySchema';

export type TeamMemberIssueDependencyListSchema = {
  /**
   * @type array
   */
  observedIssues: TeamMemberIssueDependencySchema[];
  /**
   * @type array
   */
  assignmentRequestedIssues: TeamMemberIssueDependencySchema[];
  /**
   * @type array
   */
  assignedIssues: TeamMemberIssueDependencySchema[];
  /**
   * @type array
   */
  issuesAsApprover: TeamMemberIssueDependencySchema[];
};
