/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { LoginAttemptProviderContentSchema } from './loginAttemptProviderContentSchema';

export const loginAttemptMicrosoftStrategyEnum = {
  microsoft: 'microsoft',
} as const;

export type LoginAttemptMicrosoftStrategyEnumSchema =
  (typeof loginAttemptMicrosoftStrategyEnum)[keyof typeof loginAttemptMicrosoftStrategyEnum];

export type LoginAttemptMicrosoftSchema = {
  /**
   * @type string
   */
  strategy: LoginAttemptMicrosoftStrategyEnumSchema;
  /**
   * @type object
   */
  microsoft: LoginAttemptProviderContentSchema;
};
