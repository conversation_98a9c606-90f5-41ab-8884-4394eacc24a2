/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { IssueGroupCountCollectionSchema } from './issueGroupCountCollectionSchema';
import type { IssueGroupSchema } from './issueGroupSchema';

/**
 * @example [object Object]
 */
export type IssueGroupCountSchema = {
  /**
   * @type array
   */
  groups: IssueGroupSchema[];
  /**
   * @type integer
   */
  totalCount: number;
  /**
   * @type object
   */
  groupCollection: IssueGroupCountCollectionSchema;
};
