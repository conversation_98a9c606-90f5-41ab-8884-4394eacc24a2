/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueGroupCountCollectionSchema } from './issueGroupCountCollectionSchema';
import type { IssueGroupSchema } from './issueGroupSchema';

/**
 * @example [object Object]
 */
export type IssueGroupCountSchema = {
  /**
   * @type array
   */
  groups: IssueGroupSchema[];
  /**
   * @type integer
   */
  totalCount: number;
  /**
   * @description Recursive groupCollection structure while it has sub-groups
   * @type object
   */
  groupCollection: IssueGroupCountCollectionSchema;
};
