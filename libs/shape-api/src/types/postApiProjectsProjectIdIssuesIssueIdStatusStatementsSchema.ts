/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueStatusStatementSchema } from './issueStatusStatementSchema';

export type PostApiProjectsProjectIdIssuesIssueIdStatusStatementsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue status statement created
 */
export type PostApiProjectsProjectIdIssuesIssueIdStatusStatements201Schema = IssueStatusStatementSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdStatusStatements401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesIssueIdStatusStatements403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdStatusStatements404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdStatusStatementsMutationRequestSchema = {
  /**
   * @type string | undefined, date
   */
  date?: string;
  /**
   * @type string | undefined
   */
  statement?: string;
};

export type PostApiProjectsProjectIdIssuesIssueIdStatusStatementsMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdStatusStatements201Schema;

export type PostApiProjectsProjectIdIssuesIssueIdStatusStatementsSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdStatusStatements201Schema;
  Request: PostApiProjectsProjectIdIssuesIssueIdStatusStatementsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdStatusStatementsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdStatusStatements401Schema
    | PostApiProjectsProjectIdIssuesIssueIdStatusStatements403Schema
    | PostApiProjectsProjectIdIssuesIssueIdStatusStatements404Schema;
};
