/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { CustomFieldSchema } from './customFieldSchema';
import type { ErrorSchema } from './errorSchema';

export type PatchApiProjectsProjectIdCustomFieldsCustomFieldIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  custom_field_id: string;
};

/**
 * @description Custom field updated
 */
export type PatchApiProjectsProjectIdCustomFieldsCustomFieldId200Schema = CustomFieldSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdCustomFieldsCustomFieldId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdCustomFieldsCustomFieldId403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PatchApiProjectsProjectIdCustomFieldsCustomFieldId404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdCustomFieldsCustomFieldId422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdCustomFieldsCustomFieldIdMutationRequestSchema = {
  /**
   * @type string
   */
  label: string;
};

export type PatchApiProjectsProjectIdCustomFieldsCustomFieldIdMutationResponseSchema =
  PatchApiProjectsProjectIdCustomFieldsCustomFieldId200Schema;

export type PatchApiProjectsProjectIdCustomFieldsCustomFieldIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdCustomFieldsCustomFieldId200Schema;
  Request: PatchApiProjectsProjectIdCustomFieldsCustomFieldIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdCustomFieldsCustomFieldIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdCustomFieldsCustomFieldId401Schema
    | PatchApiProjectsProjectIdCustomFieldsCustomFieldId403Schema
    | PatchApiProjectsProjectIdCustomFieldsCustomFieldId404Schema
    | PatchApiProjectsProjectIdCustomFieldsCustomFieldId422Schema;
};
