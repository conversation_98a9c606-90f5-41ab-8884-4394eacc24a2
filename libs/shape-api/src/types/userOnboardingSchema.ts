/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { TeamMemberConstructionRoleSchema } from './teamMemberConstructionRoleSchema';

export const userOnboardingKindEnum = {
  evaluator: 'evaluator',
  joiner: 'joiner',
} as const;

export type UserOnboardingKindEnumSchema = (typeof userOnboardingKindEnum)[keyof typeof userOnboardingKindEnum];

export type UserOnboardingSchema = {
  /**
   * @type object
   */
  data: object;
  /**
   * @type string, uuid
   */
  joiningProjectId: string | null;
  /**
   * @type string
   */
  kind: UserOnboardingKindEnumSchema;
  /**
   * @type object
   */
  projectLogo: {
    /**
     * @type string
     */
    url: string;
    /**
     * @type string
     */
    signedId: string;
  } | null;
  /**
   * @type string
   */
  state: string;
  /**
   * @type string
   */
  step: string | null;
  userConstructionRole: TeamMemberConstructionRoleSchema | null;
  /**
   * @type string, uuid
   */
  userId: string;
};
