/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { ShiftReportResourceAllocationSchema } from './shiftReportResourceAllocationSchema';

export type ShiftReportEquipmentSchema = {
  /**
   * @type array
   */
  activities: ShiftReportResourceAllocationSchema[];
  /**
   * @type string
   */
  description: string | null;
  /**
   * @type integer
   */
  documentCount: number;
  /**
   * @type array
   */
  downTimes: ShiftReportResourceAllocationSchema[];
  /**
   * @type string
   */
  equipmentId: string | null;
  /**
   * @type string, uuid
   */
  equipmentResourceId: string | null;
  /**
   * @type number, float
   */
  hours: number | null;
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type number, float
   */
  quantity: number | null;
  /**
   * @type integer
   */
  teamMemberId: number;
};
