/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { IssueViewListSchema } from './issueViewListSchema';

export type GetApiProjectsProjectIdIssueViewsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description List of issue views
 */
export type GetApiProjectsProjectIdIssueViews200Schema = IssueViewListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdIssueViews401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdIssueViews403Schema = unknown;

/**
 * @description Project not found
 */
export type GetApiProjectsProjectIdIssueViews404Schema = unknown;

export type GetApiProjectsProjectIdIssueViewsQueryResponseSchema = GetApiProjectsProjectIdIssueViews200Schema;

export type GetApiProjectsProjectIdIssueViewsSchemaQuery = {
  Response: GetApiProjectsProjectIdIssueViews200Schema;
  PathParams: GetApiProjectsProjectIdIssueViewsPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdIssueViews401Schema
    | GetApiProjectsProjectIdIssueViews403Schema
    | GetApiProjectsProjectIdIssueViews404Schema;
};
