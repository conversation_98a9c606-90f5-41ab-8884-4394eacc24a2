/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftReportCommentSchema } from './shiftReportCommentSchema';

export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

/**
 * @description Comment created
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators201Schema = ShiftReportCommentSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators403Schema = ErrorSchema;

/**
 * @description Shift report not found
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators422Schema = ErrorSchema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMutationRequestSchema = {
  /**
   * @description Comment body in plain text
   * @type string | undefined
   */
  plain_text?: string;
  /**
   * @type array | undefined
   */
  mentioned_team_member_ids?: number[];
  /**
   * @description Comment body in rich text
   * @type object | undefined
   */
  rich_text?: object;
  /**
   * @type array | undefined
   */
  signed_ids?: string[];
};

export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMutationResponseSchema =
  PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators201Schema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators201Schema;
  Request: PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators400Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators401Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators403Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators404Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators422Schema;
};
