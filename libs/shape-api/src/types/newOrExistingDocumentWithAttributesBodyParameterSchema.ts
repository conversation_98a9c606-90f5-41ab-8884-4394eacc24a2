/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export type NewOrExistingDocumentWithAttributesBodyParameterSchema = {
  /**
   * @description The id of an existing document
   * @type string | undefined, uuid
   */
  document_id?: string;
  /**
   * @description The signed id given by the direct upload method
   * @type string | undefined
   */
  signed_id?: string;
  /**
   * @type string
   */
  caption?: string | null;
  /**
   * @type string, uuid
   */
  location_id?: string | null;
};
