/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftReportSchema } from './shiftReportSchema';
import type { ShiftReportVisibilitySchema } from './shiftReportVisibilitySchema';

export type PatchApiProjectsProjectIdShiftReportsShiftReportIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

/**
 * @description Shift report updated
 */
export type PatchApiProjectsProjectIdShiftReportsShiftReportId200Schema = ShiftReportSchema;

/**
 * @description Bad request
 */
export type PatchApiProjectsProjectIdShiftReportsShiftReportId400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdShiftReportsShiftReportId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdShiftReportsShiftReportId403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PatchApiProjectsProjectIdShiftReportsShiftReportId404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdShiftReportsShiftReportId422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationRequestSchema = {
  /**
   * @type string
   */
  client_document_reference_number?: string | null;
  /**
   * @type array | undefined
   */
  collaborators_team_member_ids?: number[];
  /**
   * @description The signed id given by the direct upload method
   * @type string
   */
  contractor_logo_signed_id?: string | null;
  /**
   * @type string
   */
  contractor_name?: string | null;
  /**
   * @type string
   */
  internal_document_reference_number?: string | null;
  /**
   * @type string
   */
  notes?: string | null;
  /**
   * @type string
   */
  project_number?: string | null;
  /**
   * @type string | undefined, date
   */
  report_date?: string;
  /**
   * @type string
   */
  report_title?: string | null;
  /**
   * @type string, time
   */
  shift_end?: string | null;
  /**
   * @type string, time
   */
  shift_start?: string | null;
  /**
   * @type string
   */
  shift_type?: string | null;
  /**
   * @type string | undefined
   */
  visibility?: ShiftReportVisibilitySchema;
  /**
   * @type array | undefined
   */
  visibility_specific_team_ids?: string[];
  /**
   * @type string
   */
  weather_description?: string | null;
  /**
   * @type string
   */
  weather_temperature?: string | null;
  /**
   * @type array | undefined
   */
  activities?: {
    /**
     * @description When set, it marks this activity to be deleted. It requires \"id\" to be present.
     * @type string | undefined
     */
    _destroy?: string;
    /**
     * @description Temporary id, defined by the client, to allow resources to be linked to new activities.
     * @type string | undefined
     */
    _id?: string;
    /**
     * @type string
     */
    comment?: string | null;
    /**
     * @type string
     */
    description?: string | null;
    /**
     * @type string, uuid
     */
    id?: string | null;
    /**
     * @type string, uuid
     */
    location_id?: string | null;
    /**
     * @type string
     */
    planned?: string | null;
    /**
     * @type number, float
     */
    quantity?: number | null;
    /**
     * @type string, uuid
     */
    shift_activity_id?: string | null;
    /**
     * @type string
     */
    units?: string | null;
  }[];
  /**
   * @type array | undefined
   */
  contract_forces?: {
    /**
     * @description When set, it marks this person to be deleted. It requires \"id\" to be present.
     * @type string | undefined
     */
    _destroy?: string;
    /**
     * @type array | undefined
     */
    activities?: {
      /**
       * @description When set, it marks this allocation to be deleted. It requires \"id\" to be present.
       * @type string | undefined
       */
      _destroy?: string;
      /**
       * @type string, uuid
       */
      id?: string | null;
      /**
       * @type string, uuid
       */
      allocation_id?: string | null;
      /**
       * @type number
       */
      quantity?: number | null;
    }[];
    /**
     * @type string
     */
    comment?: string | null;
    /**
     * @type array | undefined
     */
    down_times?: {
      /**
       * @description When set, it marks this allocation to be deleted. It requires \"id\" to be present.
       * @type string | undefined
       */
      _destroy?: string;
      /**
       * @type string, uuid
       */
      id?: string | null;
      /**
       * @type string, uuid
       */
      allocation_id?: string | null;
      /**
       * @type number
       */
      quantity?: number | null;
    }[];
    /**
     * @type number, float
     */
    hours?: number | null;
    /**
     * @type string, uuid
     */
    id?: string | null;
    /**
     * @type string, uuid
     */
    organisation_resource_id?: string | null;
    /**
     * @type string, uuid
     */
    person_resource_id?: string | null;
    /**
     * @type string, uuid
     */
    role_resource_id?: string | null;
  }[];
  /**
   * @type array | undefined
   */
  down_times?: {
    /**
     * @description When set, it marks this down time to be deleted. It requires \"id\" to be present.
     * @type string | undefined
     */
    _destroy?: string;
    /**
     * @description Temporary id, defined by the client, to allow resources to be linked to new down_times.
     * @type string | undefined
     */
    _id?: string;
    /**
     * @type string
     */
    causal_type?: string | null;
    /**
     * @type string, uuid
     */
    id?: string | null;
    /**
     * @type string
     */
    issue_description?: string | null;
    /**
     * @type string, uuid
     */
    issue_id?: string | null;
    /**
     * @type number, float
     */
    time_lost?: number | null;
  }[];
  /**
   * @type array | undefined
   */
  equipments?: {
    /**
     * @description When set, it marks this equipment to be deleted. It requires \"id\" to be present.
     * @type string | undefined
     */
    _destroy?: string;
    /**
     * @type array | undefined
     */
    activities?: {
      /**
       * @description When set, it marks this allocation to be deleted. It requires \"id\" to be present.
       * @type string | undefined
       */
      _destroy?: string;
      /**
       * @type string, uuid
       */
      id?: string | null;
      /**
       * @type string, uuid
       */
      allocation_id?: string | null;
      /**
       * @type number
       */
      quantity?: number | null;
    }[];
    /**
     * @type array | undefined
     */
    down_times?: {
      /**
       * @description When set, it marks this allocation to be deleted. It requires \"id\" to be present.
       * @type string | undefined
       */
      _destroy?: string;
      /**
       * @type string, uuid
       */
      id?: string | null;
      /**
       * @type string, uuid
       */
      allocation_id?: string | null;
      /**
       * @type number
       */
      quantity?: number | null;
    }[];
    /**
     * @type string
     */
    equipment_id?: string | null;
    /**
     * @type string, uuid
     */
    equipment_resource_id?: string | null;
    /**
     * @type number, float
     */
    hours?: number | null;
    /**
     * @type string, uuid
     */
    id?: string | null;
    /**
     * @type number, float
     */
    quantity?: number | null;
  }[];
  /**
   * @type array | undefined
   */
  materials?: {
    /**
     * @description When set, it marks this material to be deleted. It requires \"id\" to be present.
     * @type string | undefined
     */
    _destroy?: string;
    /**
     * @type array | undefined
     */
    activities?: {
      /**
       * @description When set, it marks this allocation to be deleted. It requires \"id\" to be present.
       * @type string | undefined
       */
      _destroy?: string;
      /**
       * @type string, uuid
       */
      id?: string | null;
      /**
       * @type string, uuid
       */
      allocation_id?: string | null;
      /**
       * @type number
       */
      quantity?: number | null;
    }[];
    /**
     * @type array | undefined
     */
    down_times?: {
      /**
       * @description When set, it marks this allocation to be deleted. It requires \"id\" to be present.
       * @type string | undefined
       */
      _destroy?: string;
      /**
       * @type string, uuid
       */
      id?: string | null;
      /**
       * @type string, uuid
       */
      allocation_id?: string | null;
      /**
       * @type number
       */
      quantity?: number | null;
    }[];
    /**
     * @type string, uuid
     */
    id?: string | null;
    /**
     * @type string, uuid
     */
    material_resource_id?: string | null;
    /**
     * @type number, float
     */
    quantity?: number | null;
    /**
     * @type string
     */
    units?: string | null;
  }[];
  /**
   * @type array | undefined
   */
  safety_health_environments?: {
    /**
     * @description When set, it marks this resource to be deleted. It requires \"id\" to be present.
     * @type string | undefined
     */
    _destroy?: string;
    /**
     * @type string, uuid
     */
    id?: string | null;
    /**
     * @type string
     */
    safety_note?: string | null;
  }[];
};

export type PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationResponseSchema =
  PatchApiProjectsProjectIdShiftReportsShiftReportId200Schema;

export type PatchApiProjectsProjectIdShiftReportsShiftReportIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdShiftReportsShiftReportId200Schema;
  Request: PatchApiProjectsProjectIdShiftReportsShiftReportIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdShiftReportsShiftReportIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdShiftReportsShiftReportId400Schema
    | PatchApiProjectsProjectIdShiftReportsShiftReportId401Schema
    | PatchApiProjectsProjectIdShiftReportsShiftReportId403Schema
    | PatchApiProjectsProjectIdShiftReportsShiftReportId404Schema
    | PatchApiProjectsProjectIdShiftReportsShiftReportId422Schema;
};
