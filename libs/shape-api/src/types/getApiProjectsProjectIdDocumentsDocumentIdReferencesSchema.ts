/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DocumentAssociatedReferencesSchema } from './documentAssociatedReferencesSchema';
import type { ErrorSchema } from './errorSchema';

export type GetApiProjectsProjectIdDocumentsDocumentIdReferencesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  document_id: string;
};

/**
 * @description Document source and references
 */
export type GetApiProjectsProjectIdDocumentsDocumentIdReferences200Schema = DocumentAssociatedReferencesSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdDocumentsDocumentIdReferences401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdDocumentsDocumentIdReferences403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdDocumentsDocumentIdReferences404Schema = unknown;

export type GetApiProjectsProjectIdDocumentsDocumentIdReferencesQueryResponseSchema =
  GetApiProjectsProjectIdDocumentsDocumentIdReferences200Schema;

export type GetApiProjectsProjectIdDocumentsDocumentIdReferencesSchemaQuery = {
  Response: GetApiProjectsProjectIdDocumentsDocumentIdReferences200Schema;
  PathParams: GetApiProjectsProjectIdDocumentsDocumentIdReferencesPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdDocumentsDocumentIdReferences401Schema
    | GetApiProjectsProjectIdDocumentsDocumentIdReferences403Schema
    | GetApiProjectsProjectIdDocumentsDocumentIdReferences404Schema;
};
