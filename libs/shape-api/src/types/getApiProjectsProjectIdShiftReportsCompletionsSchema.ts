/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ShiftReportCompletionListSchema } from './shiftReportCompletionListSchema';

export type GetApiProjectsProjectIdShiftReportsCompletionsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Last 7 days of shift reports completions
 */
export type GetApiProjectsProjectIdShiftReportsCompletions200Schema = ShiftReportCompletionListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftReportsCompletions401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdShiftReportsCompletions403Schema = unknown;

export type GetApiProjectsProjectIdShiftReportsCompletionsQueryResponseSchema =
  GetApiProjectsProjectIdShiftReportsCompletions200Schema;

export type GetApiProjectsProjectIdShiftReportsCompletionsSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftReportsCompletions200Schema;
  PathParams: GetApiProjectsProjectIdShiftReportsCompletionsPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftReportsCompletions401Schema
    | GetApiProjectsProjectIdShiftReportsCompletions403Schema;
};
