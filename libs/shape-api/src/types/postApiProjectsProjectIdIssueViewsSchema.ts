/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueViewFilterItemSchema } from './issueViewFilterItemSchema';
import type { IssueViewGroupBySchema } from './issueViewGroupBySchema';
import type { IssueViewGroupPropertySchema } from './issueViewGroupPropertySchema';
import type { IssueViewSchema } from './issueViewSchema';

export type PostApiProjectsProjectIdIssueViewsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Issue view created
 */
export type PostApiProjectsProjectIdIssueViews201Schema = IssueViewSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssueViews401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssueViews403Schema = unknown;

/**
 * @description Project not found
 */
export type PostApiProjectsProjectIdIssueViews404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdIssueViews422Schema = ErrorSchema;

export const postApiProjectsProjectIdIssueViewsMutationRequestSortByEnum = {
  created: 'created',
  delay_finish: 'delay_finish',
  delay_start: 'delay_start',
  due_date: 'due_date',
  impact: 'impact',
  planned_date: 'planned_date',
  reference: 'reference',
  resolved: 'resolved',
  state: 'state',
  title: 'title',
  updated: 'updated',
} as const;

export type PostApiProjectsProjectIdIssueViewsMutationRequestSortByEnumSchema =
  (typeof postApiProjectsProjectIdIssueViewsMutationRequestSortByEnum)[keyof typeof postApiProjectsProjectIdIssueViewsMutationRequestSortByEnum];

export const postApiProjectsProjectIdIssueViewsMutationRequestSortOrderEnum = {
  asc: 'asc',
  desc: 'desc',
} as const;

export type PostApiProjectsProjectIdIssueViewsMutationRequestSortOrderEnumSchema =
  (typeof postApiProjectsProjectIdIssueViewsMutationRequestSortOrderEnum)[keyof typeof postApiProjectsProjectIdIssueViewsMutationRequestSortOrderEnum];

export type PostApiProjectsProjectIdIssueViewsMutationRequestSchema = {
  /**
   * @type string
   */
  name: string;
  /**
   * @type array
   */
  filter_properties?: IssueViewFilterItemSchema[] | null;
  group_by?: IssueViewGroupBySchema | null;
  /**
   * @type array
   */
  group_properties?: IssueViewGroupPropertySchema[] | null;
  /**
   * @type string
   */
  sort_by?: PostApiProjectsProjectIdIssueViewsMutationRequestSortByEnumSchema | null;
  /**
   * @type string
   */
  sort_order?: PostApiProjectsProjectIdIssueViewsMutationRequestSortOrderEnumSchema | null;
};

export type PostApiProjectsProjectIdIssueViewsMutationResponseSchema = PostApiProjectsProjectIdIssueViews201Schema;

export type PostApiProjectsProjectIdIssueViewsSchemaMutation = {
  Response: PostApiProjectsProjectIdIssueViews201Schema;
  Request: PostApiProjectsProjectIdIssueViewsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdIssueViewsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssueViews401Schema
    | PostApiProjectsProjectIdIssueViews403Schema
    | PostApiProjectsProjectIdIssueViews404Schema
    | PostApiProjectsProjectIdIssueViews422Schema;
};
