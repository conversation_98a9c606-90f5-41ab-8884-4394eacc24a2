/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export type TeamJoinTokenSchema = {
  /**
   * @type boolean
   */
  active: boolean | null;
  /**
   * @type integer
   */
  createdByTeamMemberId: number | null;
  /**
   * @type string, date-time
   */
  expiresAt: string | null;
  /**
   * @type string
   */
  token: string | null;
  /**
   * @type integer
   */
  usageCount: number | null;
  /**
   * @type integer
   */
  usageLimit: number | null;
};
