/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { WatchingSchema } from './watchingSchema';

export type PostApiProjectsProjectIdIssuesIssueIdWatchingsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue watched
 */
export type PostApiProjectsProjectIdIssuesIssueIdWatchings201Schema = WatchingSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdWatchings401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type PostApiProjectsProjectIdIssuesIssueIdWatchings403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdWatchings404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdWatchingsMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdWatchings201Schema;

export type PostApiProjectsProjectIdIssuesIssueIdWatchingsSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdWatchings201Schema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdWatchingsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdWatchings401Schema
    | PostApiProjectsProjectIdIssuesIssueIdWatchings403Schema
    | PostApiProjectsProjectIdIssuesIssueIdWatchings404Schema;
};
