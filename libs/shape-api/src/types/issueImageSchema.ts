/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { ImageSchema } from './imageSchema';
import type { IssueImageKindSchema } from './issueImageKindSchema';

/**
 * @deprecated
 */
export type IssueImageSchema = ImageSchema & {
  /**
   * @type string
   */
  kind: IssueImageKindSchema;
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    delete: boolean;
    /**
     * @type boolean
     */
    edit: boolean;
  };
};
