/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { ImageSchema } from './imageSchema';
import type { IssueImageKindSchema } from './issueImageKindSchema';

/**
 * @deprecated
 */
export type IssueImageSchema = ImageSchema & {
  /**
   * @type string
   */
  kind: IssueImageKindSchema;
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    delete: boolean;
    /**
     * @type boolean
     */
    edit: boolean;
  };
};
