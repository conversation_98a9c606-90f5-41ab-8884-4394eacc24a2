/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueAssignmentSchema } from './issueAssignmentSchema';

export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue assignment created
 */
export type PostApiProjectsProjectIdIssuesIssueIdAssignments201Schema = IssueAssignmentSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdIssuesIssueIdAssignments400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdAssignments401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesIssueIdAssignments403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdAssignments404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsMutationRequestSchema = {
  /**
   * @type integer | undefined
   */
  assignee_id?: number;
};

export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdAssignments201Schema;

export type PostApiProjectsProjectIdIssuesIssueIdAssignmentsSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdAssignments201Schema;
  Request: PostApiProjectsProjectIdIssuesIssueIdAssignmentsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdAssignmentsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdAssignments400Schema
    | PostApiProjectsProjectIdIssuesIssueIdAssignments401Schema
    | PostApiProjectsProjectIdIssuesIssueIdAssignments403Schema
    | PostApiProjectsProjectIdIssuesIssueIdAssignments404Schema;
};
