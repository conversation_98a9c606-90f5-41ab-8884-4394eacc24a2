/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueStateSchema } from './issueStateSchema';

export type IssueEventParametersReopenSchema = {
  /**
   * @type string
   */
  eventType: string;
  /**
   * @type object
   */
  parameters: {
    /**
     * @type string
     */
    from: IssueStateSchema;
    /**
     * @type string
     */
    to: IssueStateSchema;
  };
};
