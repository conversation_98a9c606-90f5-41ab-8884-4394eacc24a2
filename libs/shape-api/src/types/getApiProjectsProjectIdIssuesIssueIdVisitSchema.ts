/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueVisitSchema } from './issueVisitSchema';

export type GetApiProjectsProjectIdIssuesIssueIdVisitPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue visit status
 */
export type GetApiProjectsProjectIdIssuesIssueIdVisit200Schema = IssueVisitSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdIssuesIssueIdVisit401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdIssuesIssueIdVisit403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type GetApiProjectsProjectIdIssuesIssueIdVisit404Schema = unknown;

export type GetApiProjectsProjectIdIssuesIssueIdVisitQueryResponseSchema =
  GetApiProjectsProjectIdIssuesIssueIdVisit200Schema;

export type GetApiProjectsProjectIdIssuesIssueIdVisitSchemaQuery = {
  Response: GetApiProjectsProjectIdIssuesIssueIdVisit200Schema;
  PathParams: GetApiProjectsProjectIdIssuesIssueIdVisitPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdIssuesIssueIdVisit401Schema
    | GetApiProjectsProjectIdIssuesIssueIdVisit403Schema
    | GetApiProjectsProjectIdIssuesIssueIdVisit404Schema;
};
