/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { IssueCategorySchema } from './issueCategorySchema';
import type { IssueEventSchema } from './issueEventSchema';

export type ProjectIssueEventListSchema = {
  /**
   * @type array
   */
  events: (IssueEventSchema & {
    /**
     * @type object
     */
    issue: {
      category: IssueCategorySchema | null;
      /**
       * @type string, uuid
       */
      id: string;
      /**
       * @type string, uuid
       */
      locationId: string;
      /**
       * @type string
       */
      subCategory: string | null;
      /**
       * @type string
       */
      title: string;
    };
  })[];
  /**
   * @type boolean
   */
  hasMore: boolean;
};
