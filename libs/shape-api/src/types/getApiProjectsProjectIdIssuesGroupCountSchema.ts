/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueCategorySchema } from './issueCategorySchema';
import type { IssueGroupCountSchema } from './issueGroupCountSchema';
import type { IssueGroupSchema } from './issueGroupSchema';
import type { IssueImpactSchema } from './issueImpactSchema';
import type { IssueStateSchema } from './issueStateSchema';
import type { IssueVisibilityStatusSchema } from './issueVisibilityStatusSchema';

export type GetApiProjectsProjectIdIssuesGroupCountPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type GetApiProjectsProjectIdIssuesGroupCountQueryParamsSchema = {
  /**
   * @description List of issue groups. When `custom_field` is used, the custom field id also needs to be send with `groups_custom_field_id`.
   * @type array | undefined
   */
  groups?: IssueGroupSchema[];
  /**
   * @description Custom field id for when grouping by custom field
   * @type string | undefined, uuid
   */
  groups_custom_field_id?: string;
  /**
   * @description Filter issues associated with the team member
   * @type integer | undefined
   */
  associated_with?: number;
  /**
   * @description Filter by category
   * @type string | undefined
   */
  category?: IssueCategorySchema;
  /**
   * @description Filter issues closed on or before the date
   * @type string | undefined, date
   */
  closed_date_end?: string;
  /**
   * @description Filter issues closed on or after the date
   * @type string | undefined, date
   */
  closed_date_start?: string;
  /**
   * @description Filter by critical
   * @type boolean | undefined
   */
  critical?: boolean;
  /**
   * @description Used together with `custom_field_value`
   * @type string | undefined, uuid
   */
  custom_field_id?: string;
  /**
   * @description Used together with `custom_field_id`
   * @type string | undefined
   */
  custom_field_value?: string;
  /**
   * @description Filter by exact discipline node
   * @type string | undefined, uuid
   */
  discipline?: string;
  /**
   * @description Filter by discipline including its sub disciplines
   * @type string | undefined, uuid
   */
  discipline_branch?: string;
  /**
   * @description Filter issues due on or before the date
   * @type string | undefined, date
   */
  due_date_end?: string;
  /**
   * @description Filter issues due on or after the date
   * @type string | undefined, date
   */
  due_date_start?: string;
  /**
   * @description Filter by excluding state
   * @type string | undefined
   */
  filter_out_state?: IssueStateSchema;
  /**
   * @description Filter by state (\"filter_state\" is also aliased as \"state\")
   * @type string | undefined
   */
  filter_state?: IssueStateSchema;
  /**
   * @description Filter by impact
   * @type string | undefined
   */
  impact?: IssueImpactSchema;
  /**
   * @description Filter by involved team id (to be used together with visibility_status)
   * @type string | undefined, uuid
   */
  issue_involved_team_id?: string;
  /**
   * @description Filter by exact location node
   * @type string | undefined, uuid
   */
  location?: string;
  /**
   * @description Filter by location including its sub locations
   * @type string | undefined, uuid
   */
  location_branch?: string;
  /**
   * @description Filter by next actioner id
   * @type integer | undefined
   */
  next_actioner?: number;
  /**
   * @description Filter by observer id
   * @type integer | undefined
   */
  observer?: number;
  /**
   * @description Filter by observer team id
   * @type string | undefined, uuid
   */
  observer_team?: string;
  /**
   * @description Filter by overdue
   * @type boolean | undefined
   */
  overdue?: boolean;
  /**
   * @description Filter issues planned closure on or before the date
   * @type string | undefined, date
   */
  planned_closure_date_end?: string;
  /**
   * @description Filter issues planned closure on or after the date
   * @type string | undefined, date
   */
  planned_closure_date_start?: string;
  /**
   * @description Filter issues published on or before the date
   * @type string | undefined, date
   */
  published_date_end?: string;
  /**
   * @description Filter issues published on or after the date
   * @type string | undefined, date
   */
  published_date_start?: string;
  /**
   * @description Filter by responsible team member id
   * @type integer | undefined
   */
  responsible?: number;
  /**
   * @description Filter by responsible team id
   * @type string | undefined, uuid
   */
  responsible_team?: string;
  /**
   * @description Generic search query
   * @type string | undefined
   */
  search?: string;
  /**
   * @description Filter by state (\"state\" is also aliased as \"filter_state\")
   * @type string | undefined
   */
  state?: IssueStateSchema;
  /**
   * @description Filter by sub category
   * @type string | undefined
   */
  sub_category?: string;
  /**
   * @description Filter by team id
   * @type string | undefined, uuid
   */
  team_issues?: string;
  /**
   * @description Filter issues updated on or before the date
   * @type string | undefined, date
   */
  updated_date_end?: string;
  /**
   * @description Filter issues updated on or after the date
   * @type string | undefined, date
   */
  updated_date_start?: string;
  /**
   * @description Filter by team member id
   * @type integer | undefined
   */
  user_issues?: number;
  /**
   * @description Filter by visibility status
   * @type string | undefined
   */
  visibility_status?: IssueVisibilityStatusSchema;
  /**
   * @description Filter by watching team member id
   * @type integer | undefined
   */
  watching?: number;
};

/**
 * @description Issue group count
 * @example [object Object]
 */
export type GetApiProjectsProjectIdIssuesGroupCount200Schema = IssueGroupCountSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdIssuesGroupCount400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdIssuesGroupCount401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdIssuesGroupCount403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type GetApiProjectsProjectIdIssuesGroupCount404Schema = unknown;

export type GetApiProjectsProjectIdIssuesGroupCountQueryResponseSchema =
  GetApiProjectsProjectIdIssuesGroupCount200Schema;

export type GetApiProjectsProjectIdIssuesGroupCountSchemaQuery = {
  Response: GetApiProjectsProjectIdIssuesGroupCount200Schema;
  PathParams: GetApiProjectsProjectIdIssuesGroupCountPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdIssuesGroupCountQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdIssuesGroupCount400Schema
    | GetApiProjectsProjectIdIssuesGroupCount401Schema
    | GetApiProjectsProjectIdIssuesGroupCount403Schema
    | GetApiProjectsProjectIdIssuesGroupCount404Schema;
};
