/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { TeamMemberRoleSchema } from './teamMemberRoleSchema';
import type { TeamMemberStatusSchema } from './teamMemberStatusSchema';

export type ProjectSchema = {
  /**
   * @type boolean
   */
  archived: boolean;
  /**
   * @type object
   */
  channels: {
    /**
     * @type string
     */
    streamChatTeam: string;
  };
  /**
   * @type string, uuid
   */
  currentTeamId: string;
  /**
   * @type integer
   */
  currentTeamMemberId: number;
  /**
   * @type string
   */
  currentTeamMemberRole: TeamMemberRoleSchema;
  /**
   * @type string
   */
  currentTeamMemberStatus: TeamMemberStatusSchema;
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  logoUrl: string | null;
  /**
   * @type string, uuid
   */
  rootLocationId: string;
  /**
   * @type boolean
   */
  sampleProject: boolean;
  /**
   * @type string
   */
  shortName: string;
  /**
   * @type string
   */
  timezone: string;
  /**
   * @type string
   */
  title: string;
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    archive: boolean;
    /**
     * @type boolean
     */
    createIssue: boolean;
    /**
     * @type boolean
     */
    createPotentialChange: boolean;
    /**
     * @type boolean
     */
    createShiftActivity: boolean;
    /**
     * @type boolean
     */
    createShiftReport: boolean;
    /**
     * @type boolean
     */
    createWeeklyWorkPlan: boolean;
    /**
     * @type boolean
     */
    edit: boolean;
    /**
     * @type boolean
     */
    inviteTeam: boolean;
    /**
     * @type boolean
     */
    manageLocations: boolean;
    /**
     * @type boolean
     */
    manageDisciplines: boolean;
    /**
     * @type boolean
     */
    uploadDocument: boolean;
  };
};
