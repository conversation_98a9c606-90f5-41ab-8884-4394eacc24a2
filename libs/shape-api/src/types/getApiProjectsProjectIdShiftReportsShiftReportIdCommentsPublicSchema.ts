/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftReportCommentListSchema } from './shiftReportCommentListSchema';

export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicQueryParamsSchema = {
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description Comments
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic200Schema = ShiftReportCommentListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic403Schema = ErrorSchema;

/**
 * @description Shift report not found
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic404Schema = unknown;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicQueryResponseSchema =
  GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic200Schema;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic200Schema;
  PathParams: GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublicQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic400Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic401Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic403Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsPublic404Schema;
};
