/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamSchema } from './teamSchema';

export type GetApiProjectsProjectIdTeamsTeamIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
};

/**
 * @description Team
 */
export type GetApiProjectsProjectIdTeamsTeamId200Schema = TeamSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdTeamsTeamId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdTeamsTeamId403Schema = ErrorSchema;

/**
 * @description Team not found
 */
export type GetApiProjectsProjectIdTeamsTeamId404Schema = unknown;

export type GetApiProjectsProjectIdTeamsTeamIdQueryResponseSchema = GetApiProjectsProjectIdTeamsTeamId200Schema;

export type GetApiProjectsProjectIdTeamsTeamIdSchemaQuery = {
  Response: GetApiProjectsProjectIdTeamsTeamId200Schema;
  PathParams: GetApiProjectsProjectIdTeamsTeamIdPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdTeamsTeamId401Schema
    | GetApiProjectsProjectIdTeamsTeamId403Schema
    | GetApiProjectsProjectIdTeamsTeamId404Schema;
};
