/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { GroupSchema } from './groupSchema';

export type GetApiProjectsProjectIdGroupsGroupIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  group_id: string;
};

/**
 * @description Group
 */
export type GetApiProjectsProjectIdGroupsGroupId200Schema = GroupSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdGroupsGroupId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdGroupsGroupId403Schema = ErrorSchema;

/**
 * @description Group not found
 */
export type GetApiProjectsProjectIdGroupsGroupId404Schema = unknown;

export type GetApiProjectsProjectIdGroupsGroupIdQueryResponseSchema = GetApiProjectsProjectIdGroupsGroupId200Schema;

export type GetApiProjectsProjectIdGroupsGroupIdSchemaQuery = {
  Response: GetApiProjectsProjectIdGroupsGroupId200Schema;
  PathParams: GetApiProjectsProjectIdGroupsGroupIdPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdGroupsGroupId401Schema
    | GetApiProjectsProjectIdGroupsGroupId403Schema
    | GetApiProjectsProjectIdGroupsGroupId404Schema;
};
