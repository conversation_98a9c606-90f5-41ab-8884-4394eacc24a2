/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { LoginAttemptProviderContentSchema } from './loginAttemptProviderContentSchema';

export const loginAttemptGoogleStrategyEnum = {
  google: 'google',
} as const;

export type LoginAttemptGoogleStrategyEnumSchema =
  (typeof loginAttemptGoogleStrategyEnum)[keyof typeof loginAttemptGoogleStrategyEnum];

export type LoginAttemptGoogleSchema = {
  /**
   * @type string
   */
  strategy: LoginAttemptGoogleStrategyEnumSchema;
  /**
   * @type object
   */
  google: LoginAttemptProviderContentSchema;
};
