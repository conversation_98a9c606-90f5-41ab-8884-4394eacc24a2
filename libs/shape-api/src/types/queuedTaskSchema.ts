/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { QueuedTaskResultSchema } from './queuedTaskResultSchema';

export const queuedTaskStatusEnum = {
  pending: 'pending',
  expired: 'expired',
  failed: 'failed',
  completed: 'completed',
} as const;

export type QueuedTaskStatusEnumSchema = (typeof queuedTaskStatusEnum)[keyof typeof queuedTaskStatusEnum];

export type QueuedTaskSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  status: QueuedTaskStatusEnumSchema;
} & QueuedTaskResultSchema;
