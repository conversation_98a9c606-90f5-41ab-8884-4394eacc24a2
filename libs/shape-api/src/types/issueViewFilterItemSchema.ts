/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { IssueViewFilterItemValueSchema } from './issueViewFilterItemValueSchema';

export const issueViewFilterItemNameEnum = {
  archived: 'archived',
  associated_with: 'associated_with',
  category: 'category',
  closed_date_end: 'closed_date_end',
  closed_date_relative: 'closed_date_relative',
  closed_date_start: 'closed_date_start',
  critical: 'critical',
  discipline: 'discipline',
  discipline_branch: 'discipline_branch',
  due_date_end: 'due_date_end',
  due_date_relative: 'due_date_relative',
  due_date_start: 'due_date_start',
  filter_out_state: 'filter_out_state',
  filter_state: 'filter_state',
  impact: 'impact',
  issue_involved_team_id: 'issue_involved_team_id',
  location: 'location',
  location_branch: 'location_branch',
  next_actioner: 'next_actioner',
  observer: 'observer',
  observer_team: 'observer_team',
  overdue: 'overdue',
  planned_closure_date_end: 'planned_closure_date_end',
  planned_closure_date_relative: 'planned_closure_date_relative',
  planned_closure_date_start: 'planned_closure_date_start',
  published_date_end: 'published_date_end',
  published_date_relative: 'published_date_relative',
  published_date_start: 'published_date_start',
  responsible: 'responsible',
  responsible_team: 'responsible_team',
  sub_category: 'sub_category',
  team_issues: 'team_issues',
  updated_date_end: 'updated_date_end',
  updated_date_relative: 'updated_date_relative',
  updated_date_start: 'updated_date_start',
  user_issues: 'user_issues',
  visibility_status: 'visibility_status',
  watching: 'watching',
} as const;

export type IssueViewFilterItemNameEnumSchema =
  (typeof issueViewFilterItemNameEnum)[keyof typeof issueViewFilterItemNameEnum];

export type IssueViewFilterItemSchema = {
  /**
   * @type string
   */
  name: IssueViewFilterItemNameEnumSchema;
  value: IssueViewFilterItemValueSchema | IssueViewFilterItemValueSchema[];
};
