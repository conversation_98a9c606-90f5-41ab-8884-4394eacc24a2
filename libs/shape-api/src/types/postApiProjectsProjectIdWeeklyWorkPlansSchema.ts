/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { WeeklyWorkPlanSchema } from './weeklyWorkPlanSchema';

export type PostApiProjectsProjectIdWeeklyWorkPlansPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Weekly work plan created
 */
export type PostApiProjectsProjectIdWeeklyWorkPlans201Schema = WeeklyWorkPlanSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdWeeklyWorkPlans400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdWeeklyWorkPlans401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdWeeklyWorkPlans403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdWeeklyWorkPlans404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdWeeklyWorkPlans422Schema = ErrorSchema;

export type PostApiProjectsProjectIdWeeklyWorkPlansMutationRequestSchema = {
  /**
   * @type string, date
   */
  end_date: string;
  /**
   * @type string, date
   */
  start_date: string;
  /**
   * @type string
   */
  title: string;
};

export type PostApiProjectsProjectIdWeeklyWorkPlansMutationResponseSchema =
  PostApiProjectsProjectIdWeeklyWorkPlans201Schema;

export type PostApiProjectsProjectIdWeeklyWorkPlansSchemaMutation = {
  Response: PostApiProjectsProjectIdWeeklyWorkPlans201Schema;
  Request: PostApiProjectsProjectIdWeeklyWorkPlansMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdWeeklyWorkPlansPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdWeeklyWorkPlans400Schema
    | PostApiProjectsProjectIdWeeklyWorkPlans401Schema
    | PostApiProjectsProjectIdWeeklyWorkPlans403Schema
    | PostApiProjectsProjectIdWeeklyWorkPlans404Schema
    | PostApiProjectsProjectIdWeeklyWorkPlans422Schema;
};
