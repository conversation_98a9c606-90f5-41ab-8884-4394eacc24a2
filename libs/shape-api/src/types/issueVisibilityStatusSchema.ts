/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export const issueVisibilityStatusEnum = {
  internal: 'internal',
  specific_teams: 'specific_teams',
  project_wide: 'project_wide',
} as const;

export type IssueVisibilityStatusEnumSchema =
  (typeof issueVisibilityStatusEnum)[keyof typeof issueVisibilityStatusEnum];

export type IssueVisibilityStatusSchema = IssueVisibilityStatusEnumSchema;
