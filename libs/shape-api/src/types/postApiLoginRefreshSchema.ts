/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { ErrorSchema } from './errorSchema';

export type PostApiLoginRefreshHeaderParamsSchema = {
  /**
   * @description Authorization refresh token
   * @type string
   */
  'Authorization-Refresh': string;
};

/**
 * @description Authentication refreshed
 */
export type PostApiLoginRefresh204Schema = unknown;

/**
 * @description Bad request
 */
export type PostApiLoginRefresh400Schema = ErrorSchema;

/**
 * @description Unauthorized
 */
export type PostApiLoginRefresh401Schema = ErrorSchema;

export type PostApiLoginRefreshMutationResponseSchema = PostApiLoginRefresh204Schema;

export type PostApiLoginRefreshSchemaMutation = {
  Response: PostApiLoginRefresh204Schema;
  HeaderParams: PostApiLoginRefreshHeaderParamsSchema;
  Errors: PostApiLoginRefresh400Schema | PostApiLoginRefresh401Schema;
};
