/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { UserProductTourSchema } from './userProductTourSchema';

export type PatchApiProductToursProductTourKeyPathParamsSchema = {
  /**
   * @type string
   */
  product_tour_key: string;
};

/**
 * @description ProductTour updated
 */
export type PatchApiProductToursProductTourKey200Schema = UserProductTourSchema;

/**
 * @description Authentication required
 */
export type PatchApiProductToursProductTourKey401Schema = AuthenticationErrorSchema;

/**
 * @description Not found
 */
export type PatchApiProductToursProductTourKey404Schema = unknown;

export type PatchApiProductToursProductTourKeyMutationRequestSchema = {
  /**
   * @type string | undefined, date-time
   */
  started_at?: string;
  /**
   * @type string | undefined, date-time
   */
  finished_at?: string;
  /**
   * @type string | undefined, date-time
   */
  dismissed_at?: string;
};

export type PatchApiProductToursProductTourKeyMutationResponseSchema = PatchApiProductToursProductTourKey200Schema;

export type PatchApiProductToursProductTourKeySchemaMutation = {
  Response: PatchApiProductToursProductTourKey200Schema;
  Request: PatchApiProductToursProductTourKeyMutationRequestSchema;
  PathParams: PatchApiProductToursProductTourKeyPathParamsSchema;
  Errors: PatchApiProductToursProductTourKey401Schema | PatchApiProductToursProductTourKey404Schema;
};
