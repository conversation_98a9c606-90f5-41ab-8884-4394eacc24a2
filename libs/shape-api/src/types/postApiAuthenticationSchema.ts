/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationStrategyTypeSchema } from './authenticationStrategyTypeSchema';
import type { ErrorSchema } from './errorSchema';
import type { LoginAttemptSchema } from './loginAttemptSchema';

/**
 * @description Account checked
 */
export type PostApiAuthentication200Schema = LoginAttemptSchema;

/**
 * @description Attempt failed
 */
export type PostApiAuthentication400Schema = ErrorSchema;

export type PostApiAuthenticationMutationRequestSchema = {
  /**
   * @type string
   */
  strategy: AuthenticationStrategyTypeSchema;
  /**
   * @type string | undefined
   */
  email?: string;
  /**
   * @type string | undefined
   */
  token?: string;
};

export type PostApiAuthenticationMutationResponseSchema = PostApiAuthentication200Schema;

export type PostApiAuthenticationSchemaMutation = {
  Response: PostApiAuthentication200Schema;
  Request: PostApiAuthenticationMutationRequestSchema;
  Errors: PostApiAuthentication400Schema;
};
