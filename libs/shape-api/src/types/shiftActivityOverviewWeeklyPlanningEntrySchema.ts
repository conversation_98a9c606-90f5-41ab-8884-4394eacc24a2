/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export type ShiftActivityOverviewWeeklyPlanningEntrySchema = {
  /**
   * @type string, date
   */
  startDate: string;
  /**
   * @type string, date
   */
  endDate: string;
  /**
   * @type number, float
   */
  expectedPercentageCompleted: number | null;
  /**
   * @type number, float
   */
  actualPercentageCompleted: number | null;
  /**
   * @type string
   */
  varianceCategory: string | null;
  /**
   * @type string
   */
  varianceRemarks: string | null;
  /**
   * @type string
   */
  mitigationMeasures: string | null;
  /**
   * @type integer
   */
  authorId: number;
  /**
   * @type string, uuid
   */
  weeklyWorkPlanId: string;
  /**
   * @type string
   */
  weeklyWorkPlanTitle: string;
};
