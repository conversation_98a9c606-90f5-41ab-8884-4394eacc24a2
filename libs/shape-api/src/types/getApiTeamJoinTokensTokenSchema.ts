/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { TeamJoinTokenPublicSchema } from './teamJoinTokenPublicSchema';

export type GetApiTeamJoinTokensTokenPathParamsSchema = {
  /**
   * @type string
   */
  token: string;
};

/**
 * @description Team join token
 */
export type GetApiTeamJoinTokensToken200Schema = TeamJoinTokenPublicSchema;

/**
 * @description Not found
 */
export type GetApiTeamJoinTokensToken404Schema = unknown;

export type GetApiTeamJoinTokensTokenQueryResponseSchema = GetApiTeamJoinTokensToken200Schema;

export type GetApiTeamJoinTokensTokenSchemaQuery = {
  Response: GetApiTeamJoinTokensToken200Schema;
  PathParams: GetApiTeamJoinTokensTokenPathParamsSchema;
  Errors: GetApiTeamJoinTokensToken404Schema;
};
