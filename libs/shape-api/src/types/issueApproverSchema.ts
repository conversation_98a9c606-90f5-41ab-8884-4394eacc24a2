/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueApproverStatusSchema } from './issueApproverStatusSchema';
import type { TeamBasicDetailsSchema } from './teamBasicDetailsSchema';
import type { UserBasicDetailsSchema } from './userBasicDetailsSchema';

export type IssueApproverSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type integer
   */
  teamMemberId: number;
  /**
   * @type string | undefined, uuid
   */
  teamMemberNewId?: string;
  /**
   * @type integer
   */
  sortOrder: number;
  /**
   * @type string, date-time
   */
  approvedAt: string | null;
  /**
   * @type string
   */
  status: IssueApproverStatusSchema;
  /**
   * @type object
   */
  user: UserBasicDetailsSchema;
  /**
   * @type object
   */
  team: TeamBasicDetailsSchema;
};
