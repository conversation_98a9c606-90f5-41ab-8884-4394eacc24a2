/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { WeeklyWorkPlanActivitySchema } from './weeklyWorkPlanActivitySchema';
import type { WeeklyWorkPlanActivityStatusesSchema } from './weeklyWorkPlanActivityStatusesSchema';
import type { WeeklyWorkPlanActivityVarianceCategoriesSchema } from './weeklyWorkPlanActivityVarianceCategoriesSchema';

export type PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
  /**
   * @type string, uuid
   */
  activity_id: string;
};

/**
 * @description Weekly work plan activity updated
 */
export type PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId200Schema =
  WeeklyWorkPlanActivitySchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId422Schema = unknown;

export type PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdMutationRequestSchema = {
  /**
   * @type string
   */
  comment?: string | null;
  /**
   * @type number, float
   */
  expected_percentage_completed?: number | null;
  status?: WeeklyWorkPlanActivityStatusesSchema | null;
  variance_category?: WeeklyWorkPlanActivityVarianceCategoriesSchema | null;
  /**
   * @type string
   */
  variance_recovery_mitigation_measures?: string | null;
  /**
   * @type string
   */
  variance_remarks?: string | null;
};

export type PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdMutationResponseSchema =
  PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId200Schema;

export type PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId200Schema;
  Request: PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId401Schema
    | PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId403Schema
    | PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId404Schema
    | PatchApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesActivityId422Schema;
};
