/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamSubscriptionPlanSchema } from './teamSubscriptionPlanSchema';

export type GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlanPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
};

/**
 * @description Team subscription plan
 */
export type GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlan200Schema = TeamSubscriptionPlanSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlan401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlan403Schema = ErrorSchema;

/**
 * @description Team not found
 */
export type GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlan404Schema = unknown;

export type GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlanQueryResponseSchema =
  GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlan200Schema;

export type GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlanSchemaQuery = {
  Response: GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlan200Schema;
  PathParams: GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlanPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlan401Schema
    | GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlan403Schema
    | GetApiProjectsProjectIdTeamsTeamIdSubscriptionPlan404Schema;
};
