/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export type IssueDetailsUpdatesCountSchema = {
  /**
   * @type object
   */
  updates: {
    /**
     * @type string, date-time
     */
    lastVisitedAt: string | null;
    /**
     * @type object
     */
    unreadUpdatesCount: {
      /**
       * @type integer
       */
      public: number;
      /**
       * @type integer
       */
      team: number;
    };
  };
};
