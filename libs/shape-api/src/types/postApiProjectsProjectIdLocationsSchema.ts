/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { LocationSchema } from './locationSchema';

export type PostApiProjectsProjectIdLocationsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Location created
 */
export type PostApiProjectsProjectIdLocations201Schema = LocationSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdLocations401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdLocations403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type PostApiProjectsProjectIdLocations404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdLocations422Schema = ErrorSchema;

export type PostApiProjectsProjectIdLocationsMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  name?: string;
  /**
   * @type string | undefined
   */
  short_code?: string;
  /**
   * @type string | undefined, uuid
   */
  parent_location_id?: string;
  /**
   * @type integer | undefined
   */
  sort_position?: number;
};

export type PostApiProjectsProjectIdLocationsMutationResponseSchema = PostApiProjectsProjectIdLocations201Schema;

export type PostApiProjectsProjectIdLocationsSchemaMutation = {
  Response: PostApiProjectsProjectIdLocations201Schema;
  Request: PostApiProjectsProjectIdLocationsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdLocationsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdLocations401Schema
    | PostApiProjectsProjectIdLocations403Schema
    | PostApiProjectsProjectIdLocations404Schema
    | PostApiProjectsProjectIdLocations422Schema;
};
