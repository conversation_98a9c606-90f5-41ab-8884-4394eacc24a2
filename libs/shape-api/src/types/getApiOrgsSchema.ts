/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { OrgListSchema } from './orgListSchema';

/**
 * @description List of orgs
 */
export type GetApiOrgs200Schema = OrgListSchema;

/**
 * @description Authentication required
 */
export type GetApiOrgs401Schema = AuthenticationErrorSchema;

export type GetApiOrgsQueryResponseSchema = GetApiOrgs200Schema;

export type GetApiOrgsSchemaQuery = {
  Response: GetApiOrgs200Schema;
  Errors: GetApiOrgs401Schema;
};
