/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DocumentKindSchema } from './documentKindSchema';
import type { DocumentReferenceAndDocumentListSchema } from './documentReferenceAndDocumentListSchema';
import type { ErrorSchema } from './errorSchema';
import type { OneOrManyIntegerSchema } from './oneOrManyIntegerSchema';
import type { OneOrManyUuidNullableSchema } from './oneOrManyUuidNullableSchema';

export const getApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsPathParamsResourceTypeEnum =
  {
    activities: 'activities',
    contract_forces: 'contract_forces',
    down_times: 'down_times',
    equipments: 'equipments',
    materials: 'materials',
    safety_health_environments: 'safety_health_environments',
  } as const;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsPathParamsResourceTypeEnumSchema =
  (typeof getApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsPathParamsResourceTypeEnum)[keyof typeof getApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsPathParamsResourceTypeEnum];

export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
  /**
   * @type string
   */
  resource_type: GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsPathParamsResourceTypeEnumSchema;
  /**
   * @type string, uuid
   */
  resource_id: string;
};

export const getApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsQueryParamsSortOrderEnum = {
  asc: 'asc',
  desc: 'desc',
} as const;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsQueryParamsSortOrderEnumSchema =
  (typeof getApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsQueryParamsSortOrderEnum)[keyof typeof getApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsQueryParamsSortOrderEnum];

export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsQueryParamsSchema = {
  /**
   * @type string | undefined, date-time
   */
  created_end?: string;
  /**
   * @type string | undefined, date-time
   */
  created_start?: string;
  /**
   * @type string | undefined
   */
  kind?: DocumentKindSchema;
  location_id?: OneOrManyUuidNullableSchema;
  team_member_id?: OneOrManyIntegerSchema;
  /**
   * @description Generic search query
   * @type string | undefined
   */
  search?: string;
  /**
   * @description Order of the results
   * @type string | undefined
   */
  sort_order?: GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsQueryParamsSortOrderEnumSchema;
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of shift report resource documents
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments200Schema =
  DocumentReferenceAndDocumentListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments404Schema = unknown;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsQueryResponseSchema =
  GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments200Schema;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments200Schema;
  PathParams: GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments400Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments401Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments403Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments404Schema;
};
