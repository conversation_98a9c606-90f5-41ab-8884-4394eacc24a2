/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersNewIssueStatusStatementTypeEnum = {
  new_issue_status_statement: 'new_issue_status_statement',
} as const;

export type NotificationParametersNewIssueStatusStatementTypeEnumSchema =
  (typeof notificationParametersNewIssueStatusStatementTypeEnum)[keyof typeof notificationParametersNewIssueStatusStatementTypeEnum];

export type NotificationParametersNewIssueStatusStatementSchema = {
  /**
   * @type string
   */
  type: NotificationParametersNewIssueStatusStatementTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string | undefined, date
     */
    date?: string;
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type string, uuid
     */
    issueStatusStatementId: string;
    /**
     * @type string, uuid
     */
    projectId: string;
    /**
     * @type string
     */
    statement: string;
  };
};
