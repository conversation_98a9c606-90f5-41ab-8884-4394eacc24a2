/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DashboardListSchema } from './dashboardListSchema';
import type { ErrorSchema } from './errorSchema';

export type GetApiProjectsProjectIdDashboardsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Dashboards
 */
export type GetApiProjectsProjectIdDashboards200Schema = DashboardListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdDashboards401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdDashboards403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdDashboards404Schema = unknown;

export type GetApiProjectsProjectIdDashboardsQueryResponseSchema = GetApiProjectsProjectIdDashboards200Schema;

export type GetApiProjectsProjectIdDashboardsSchemaQuery = {
  Response: GetApiProjectsProjectIdDashboards200Schema;
  PathParams: GetApiProjectsProjectIdDashboardsPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdDashboards401Schema
    | GetApiProjectsProjectIdDashboards403Schema
    | GetApiProjectsProjectIdDashboards404Schema;
};
