/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationParametersAddedToProjectSchema } from './notificationParametersAddedToProjectSchema';
import type { NotificationParametersIssueCommentMentionSchema } from './notificationParametersIssueCommentMentionSchema';
import type { NotificationParametersIssueNeedsYourApprovalSchema } from './notificationParametersIssueNeedsYourApprovalSchema';
import type { NotificationParametersIssueWasReopenedSchema } from './notificationParametersIssueWasReopenedSchema';
import type { NotificationParametersIssueWasResolvedSchema } from './notificationParametersIssueWasResolvedSchema';
import type { NotificationParametersNewIssueCommentSchema } from './notificationParametersNewIssueCommentSchema';
import type { NotificationParametersNewIssuePrivateCommentSchema } from './notificationParametersNewIssuePrivateCommentSchema';
import type { NotificationParametersNewIssueStatusStatementSchema } from './notificationParametersNewIssueStatusStatementSchema';
import type { NotificationParametersNewShiftReportCommentSchema } from './notificationParametersNewShiftReportCommentSchema';
import type { NotificationParametersShiftReportCommentMentionSchema } from './notificationParametersShiftReportCommentMentionSchema';
import type { NotificationParametersYourIssueApprovalRequestWasAcceptedSchema } from './notificationParametersYourIssueApprovalRequestWasAcceptedSchema';
import type { NotificationParametersYourIssueApprovalRequestWasRejectedSchema } from './notificationParametersYourIssueApprovalRequestWasRejectedSchema';
import type { NotificationParametersYourIssueAssignmentWasAcceptedSchema } from './notificationParametersYourIssueAssignmentWasAcceptedSchema';
import type { NotificationParametersYourIssueAssignmentWasRejectedSchema } from './notificationParametersYourIssueAssignmentWasRejectedSchema';
import type { NotificationParametersYourIssueWasReassignedSchema } from './notificationParametersYourIssueWasReassignedSchema';
import type { NotificationParametersYouWereAssignedToIssueSchema } from './notificationParametersYouWereAssignedToIssueSchema';

export type NotificationParametersSchema =
  | NotificationParametersAddedToProjectSchema
  | NotificationParametersIssueCommentMentionSchema
  | NotificationParametersIssueNeedsYourApprovalSchema
  | NotificationParametersIssueWasReopenedSchema
  | NotificationParametersIssueWasResolvedSchema
  | NotificationParametersNewIssueCommentSchema
  | NotificationParametersNewIssuePrivateCommentSchema
  | NotificationParametersNewShiftReportCommentSchema
  | NotificationParametersNewIssueStatusStatementSchema
  | NotificationParametersShiftReportCommentMentionSchema
  | NotificationParametersYourIssueApprovalRequestWasAcceptedSchema
  | NotificationParametersYourIssueApprovalRequestWasRejectedSchema
  | NotificationParametersYourIssueAssignmentWasAcceptedSchema
  | NotificationParametersYourIssueAssignmentWasRejectedSchema
  | NotificationParametersYourIssueWasReassignedSchema
  | NotificationParametersYouWereAssignedToIssueSchema;
