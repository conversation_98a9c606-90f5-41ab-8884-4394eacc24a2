/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { NotificationParametersAddedToProjectSchema } from './notificationParametersAddedToProjectSchema';
import type { NotificationParametersIssueCommentMentionSchema } from './notificationParametersIssueCommentMentionSchema';
import type { NotificationParametersIssueNeedsYourApprovalSchema } from './notificationParametersIssueNeedsYourApprovalSchema';
import type { NotificationParametersIssueWasReopenedSchema } from './notificationParametersIssueWasReopenedSchema';
import type { NotificationParametersIssueWasResolvedSchema } from './notificationParametersIssueWasResolvedSchema';
import type { NotificationParametersNewIssueCommentSchema } from './notificationParametersNewIssueCommentSchema';
import type { NotificationParametersNewIssuePrivateCommentSchema } from './notificationParametersNewIssuePrivateCommentSchema';
import type { NotificationParametersNewIssueStatusStatementSchema } from './notificationParametersNewIssueStatusStatementSchema';
import type { NotificationParametersNewShiftReportCommentSchema } from './notificationParametersNewShiftReportCommentSchema';
import type { NotificationParametersShiftReportCommentMentionSchema } from './notificationParametersShiftReportCommentMentionSchema';
import type { NotificationParametersYourIssueApprovalRequestWasAcceptedSchema } from './notificationParametersYourIssueApprovalRequestWasAcceptedSchema';
import type { NotificationParametersYourIssueApprovalRequestWasRejectedSchema } from './notificationParametersYourIssueApprovalRequestWasRejectedSchema';
import type { NotificationParametersYourIssueAssignmentWasAcceptedSchema } from './notificationParametersYourIssueAssignmentWasAcceptedSchema';
import type { NotificationParametersYourIssueAssignmentWasRejectedSchema } from './notificationParametersYourIssueAssignmentWasRejectedSchema';
import type { NotificationParametersYourIssueWasReassignedSchema } from './notificationParametersYourIssueWasReassignedSchema';
import type { NotificationParametersYouWereAssignedToIssueSchema } from './notificationParametersYouWereAssignedToIssueSchema';

export type NotificationParametersSchema =
  | (NotificationParametersAddedToProjectSchema & {
      type: 'added_to_project';
    })
  | (NotificationParametersIssueCommentMentionSchema & {
      type: 'issue_comment_mention';
    })
  | (NotificationParametersIssueNeedsYourApprovalSchema & {
      type: 'issue_needs_your_approval';
    })
  | (NotificationParametersIssueWasReopenedSchema & {
      type: 'issue_was_reopened';
    })
  | (NotificationParametersIssueWasResolvedSchema & {
      type: 'issue_was_resolved';
    })
  | (NotificationParametersNewIssueCommentSchema & {
      type: 'new_issue_comment';
    })
  | (NotificationParametersNewIssuePrivateCommentSchema & {
      type: 'new_issue_private_comment';
    })
  | (NotificationParametersNewShiftReportCommentSchema & {
      type: 'new_shift_report_comment';
    })
  | (NotificationParametersNewIssueStatusStatementSchema & {
      type: 'new_issue_status_statement';
    })
  | (NotificationParametersShiftReportCommentMentionSchema & {
      type: 'shift_report_comment_mention';
    })
  | (NotificationParametersYouWereAssignedToIssueSchema & {
      type: 'you_were_assigned_to_issue';
    })
  | (NotificationParametersYourIssueApprovalRequestWasAcceptedSchema & {
      type: 'your_issue_approval_request_was_accepted';
    })
  | (NotificationParametersYourIssueApprovalRequestWasRejectedSchema & {
      type: 'your_issue_approval_request_was_rejected';
    })
  | (NotificationParametersYourIssueAssignmentWasAcceptedSchema & {
      type: 'your_issue_assignment_was_accepted';
    })
  | (NotificationParametersYourIssueAssignmentWasRejectedSchema & {
      type: 'your_issue_assignment_was_rejected';
    })
  | (NotificationParametersYourIssueWasReassignedSchema & {
      type: 'your_issue_was_reassigned';
    });
