/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { ShiftActivityOverviewDailyProgressEntryDiscriminatedPartialSchema } from './shiftActivityOverviewDailyProgressEntryDiscriminatedPartialSchema';

export type ShiftActivityOverviewDailyProgressEntrySchema = {
  /**
   * @type integer
   */
  authorId: number;
  /**
   * @type string, date-time
   */
  createdAt: string;
  /**
   * @type string, date
   */
  date: string;
  /**
   * @type string
   */
  description: string;
  /**
   * @type integer
   */
  documentCount: number;
  /**
   * @type string, uuid
   */
  locationId: string;
  /**
   * @type number, float
   */
  percentageCompleted: number | null;
  /**
   * @type string
   */
  planned: string | null;
  /**
   * @deprecated
   * @type string
   */
  progressUnits: string | null;
  /**
   * @deprecated
   * @type number, float
   */
  progressValue: number | null;
  /**
   * @type number, float
   */
  quantity: number | null;
  /**
   * @type string
   */
  units: string | null;
} & ShiftActivityOverviewDailyProgressEntryDiscriminatedPartialSchema;
