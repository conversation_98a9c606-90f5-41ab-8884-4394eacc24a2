/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { MetabaseDashboardSchema } from './metabaseDashboardSchema';

export type GetApiProjectsProjectIdDashboardsMetabaseDashboardKeyPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string
   */
  dashboard_key: string;
};

/**
 * @description Dashboard token
 */
export type GetApiProjectsProjectIdDashboardsMetabaseDashboardKey200Schema = MetabaseDashboardSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdDashboardsMetabaseDashboardKey401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdDashboardsMetabaseDashboardKey403Schema = ErrorSchema;

/**
 * @description Dashboard not found
 */
export type GetApiProjectsProjectIdDashboardsMetabaseDashboardKey404Schema = unknown;

/**
 * @description Metabase not configured
 */
export type GetApiProjectsProjectIdDashboardsMetabaseDashboardKey503Schema = unknown;

export type GetApiProjectsProjectIdDashboardsMetabaseDashboardKeyQueryResponseSchema =
  GetApiProjectsProjectIdDashboardsMetabaseDashboardKey200Schema;

export type GetApiProjectsProjectIdDashboardsMetabaseDashboardKeySchemaQuery = {
  Response: GetApiProjectsProjectIdDashboardsMetabaseDashboardKey200Schema;
  PathParams: GetApiProjectsProjectIdDashboardsMetabaseDashboardKeyPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdDashboardsMetabaseDashboardKey401Schema
    | GetApiProjectsProjectIdDashboardsMetabaseDashboardKey403Schema
    | GetApiProjectsProjectIdDashboardsMetabaseDashboardKey404Schema
    | GetApiProjectsProjectIdDashboardsMetabaseDashboardKey503Schema;
};
