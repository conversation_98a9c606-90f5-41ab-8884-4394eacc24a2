/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueCategorySchema } from './issueCategorySchema';
import type { IssueImpactSchema } from './issueImpactSchema';
import type { IssueStateSchema } from './issueStateSchema';
import type { IssueVisibilityStatusSchema } from './issueVisibilityStatusSchema';
import type { QueuedTaskSchema } from './queuedTaskSchema';

export type PostApiProjectsProjectIdIssuesExportPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export const postApiProjectsProjectIdIssuesExportQueryParamsFileTypeEnum = {
  csv: 'csv',
  pdf: 'pdf',
  xlsx: 'xlsx',
} as const;

export type PostApiProjectsProjectIdIssuesExportQueryParamsFileTypeEnumSchema =
  (typeof postApiProjectsProjectIdIssuesExportQueryParamsFileTypeEnum)[keyof typeof postApiProjectsProjectIdIssuesExportQueryParamsFileTypeEnum];

export type PostApiProjectsProjectIdIssuesExportQueryParamsSchema = {
  /**
   * @type string
   */
  file_type: PostApiProjectsProjectIdIssuesExportQueryParamsFileTypeEnumSchema;
  /**
   * @type string | undefined
   */
  report_title?: string;
  /**
   * @description Filter issues associated with the team member
   * @type integer | undefined
   */
  associated_with?: number;
  /**
   * @description Filter by category
   * @type string | undefined
   */
  category?: IssueCategorySchema;
  /**
   * @description Filter issues closed on or before the date
   * @type string | undefined, date
   */
  closed_date_end?: string;
  /**
   * @description Filter issues closed on or after the date
   * @type string | undefined, date
   */
  closed_date_start?: string;
  /**
   * @description Filter by critical
   * @type boolean | undefined
   */
  critical?: boolean;
  /**
   * @description Used together with `custom_field_value`
   * @type string | undefined, uuid
   */
  custom_field_id?: string;
  /**
   * @description Used together with `custom_field_id`
   * @type string | undefined
   */
  custom_field_value?: string;
  /**
   * @description Filter by exact discipline node
   * @type string | undefined, uuid
   */
  discipline?: string;
  /**
   * @description Filter by discipline including its sub disciplines
   * @type string | undefined, uuid
   */
  discipline_branch?: string;
  /**
   * @description Filter issues due on or before the date
   * @type string | undefined, date
   */
  due_date_end?: string;
  /**
   * @description Filter issues due on or after the date
   * @type string | undefined, date
   */
  due_date_start?: string;
  /**
   * @description Filter by excluding state
   * @type string | undefined
   */
  filter_out_state?: IssueStateSchema;
  /**
   * @description Filter by state (\"filter_state\" is also aliased as \"state\")
   * @type string | undefined
   */
  filter_state?: IssueStateSchema;
  /**
   * @description Filter by impact
   * @type string | undefined
   */
  impact?: IssueImpactSchema;
  /**
   * @description Filter by involved team id (to be used together with visibility_status)
   * @type string | undefined, uuid
   */
  issue_involved_team_id?: string;
  /**
   * @description Filter by exact location node
   * @type string | undefined, uuid
   */
  location?: string;
  /**
   * @description Filter by location including its sub locations
   * @type string | undefined, uuid
   */
  location_branch?: string;
  /**
   * @description Filter by next actioner id
   * @type integer | undefined
   */
  next_actioner?: number;
  /**
   * @description Filter by observer id
   * @type integer | undefined
   */
  observer?: number;
  /**
   * @description Filter by observer team id
   * @type string | undefined, uuid
   */
  observer_team?: string;
  /**
   * @description Filter by overdue
   * @type boolean | undefined
   */
  overdue?: boolean;
  /**
   * @description Filter issues planned closure on or before the date
   * @type string | undefined, date
   */
  planned_closure_date_end?: string;
  /**
   * @description Filter issues planned closure on or after the date
   * @type string | undefined, date
   */
  planned_closure_date_start?: string;
  /**
   * @description Filter issues published on or before the date
   * @type string | undefined, date
   */
  published_date_end?: string;
  /**
   * @description Filter issues published on or after the date
   * @type string | undefined, date
   */
  published_date_start?: string;
  /**
   * @description Filter by responsible team member id
   * @type integer | undefined
   */
  responsible?: number;
  /**
   * @description Filter by responsible team id
   * @type string | undefined, uuid
   */
  responsible_team?: string;
  /**
   * @description Generic search query
   * @type string | undefined
   */
  search?: string;
  /**
   * @description Filter by state (\"state\" is also aliased as \"filter_state\")
   * @type string | undefined
   */
  state?: IssueStateSchema;
  /**
   * @description Filter by sub category
   * @type string | undefined
   */
  sub_category?: string;
  /**
   * @description Filter by team id
   * @type string | undefined, uuid
   */
  team_issues?: string;
  /**
   * @description Filter issues updated on or before the date
   * @type string | undefined, date
   */
  updated_date_end?: string;
  /**
   * @description Filter issues updated on or after the date
   * @type string | undefined, date
   */
  updated_date_start?: string;
  /**
   * @description Filter by team member id
   * @type integer | undefined
   */
  user_issues?: number;
  /**
   * @description Filter by visibility status
   * @type string | undefined
   */
  visibility_status?: IssueVisibilityStatusSchema;
  /**
   * @description Filter by watching team member id
   * @type integer | undefined
   */
  watching?: number;
};

/**
 * @description Request accepted
 */
export type PostApiProjectsProjectIdIssuesExport202Schema = QueuedTaskSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdIssuesExport400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesExport401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesExport403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type PostApiProjectsProjectIdIssuesExport404Schema = unknown;

export type PostApiProjectsProjectIdIssuesExportMutationResponseSchema = PostApiProjectsProjectIdIssuesExport202Schema;

export type PostApiProjectsProjectIdIssuesExportSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesExport202Schema;
  PathParams: PostApiProjectsProjectIdIssuesExportPathParamsSchema;
  QueryParams: PostApiProjectsProjectIdIssuesExportQueryParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesExport400Schema
    | PostApiProjectsProjectIdIssuesExport401Schema
    | PostApiProjectsProjectIdIssuesExport403Schema
    | PostApiProjectsProjectIdIssuesExport404Schema;
};
