/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DisciplineSchema } from './disciplineSchema';
import type { ErrorSchema } from './errorSchema';

export type PatchApiProjectsProjectIdDisciplinesDisciplineIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  discipline_id: string;
};

/**
 * @description Discipline updated
 */
export type PatchApiProjectsProjectIdDisciplinesDisciplineId200Schema = DisciplineSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdDisciplinesDisciplineId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdDisciplinesDisciplineId403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PatchApiProjectsProjectIdDisciplinesDisciplineId404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdDisciplinesDisciplineId422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdDisciplinesDisciplineIdMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  name?: string;
  /**
   * @type string | undefined
   */
  short_code?: string;
  /**
   * @type string | undefined, uuid
   */
  parent_discipline_id?: string;
  /**
   * @type integer | undefined
   */
  sort_position?: number;
};

export type PatchApiProjectsProjectIdDisciplinesDisciplineIdMutationResponseSchema =
  PatchApiProjectsProjectIdDisciplinesDisciplineId200Schema;

export type PatchApiProjectsProjectIdDisciplinesDisciplineIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdDisciplinesDisciplineId200Schema;
  Request: PatchApiProjectsProjectIdDisciplinesDisciplineIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdDisciplinesDisciplineIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdDisciplinesDisciplineId401Schema
    | PatchApiProjectsProjectIdDisciplinesDisciplineId403Schema
    | PatchApiProjectsProjectIdDisciplinesDisciplineId404Schema
    | PatchApiProjectsProjectIdDisciplinesDisciplineId422Schema;
};
