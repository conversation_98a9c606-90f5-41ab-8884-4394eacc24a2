/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string
   */
  message_id: string;
};

/**
 * @description Request accepted
 */
export type PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments202Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments404Schema = unknown;

/**
 * @description Request failed
 */
export type PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments422Schema = ErrorSchema;

export type PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsMutationResponseSchema =
  PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments202Schema;

export type PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsSchemaMutation = {
  Response: PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments202Schema;
  PathParams: PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachmentsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments401Schema
    | PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments403Schema
    | PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments404Schema
    | PostApiProjectsProjectIdChannelsMessagesMessageIdSaveAttachments422Schema;
};
