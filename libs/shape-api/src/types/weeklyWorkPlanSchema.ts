/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { WeeklyWorkPlanStatusesSchema } from './weeklyWorkPlanStatusesSchema';

export type WeeklyWorkPlanSchema = {
  /**
   * @type boolean
   */
  archived: boolean;
  /**
   * @type string, date
   */
  endDate: string;
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string, date-time
   */
  lastActivityAt: string;
  /**
   * @type number, float
   */
  ppc: number | null;
  /**
   * @type string, date-time
   */
  publishedAt: string | null;
  /**
   * @type string, date
   */
  startDate: string;
  /**
   * @type string
   */
  status: WeeklyWorkPlanStatusesSchema;
  /**
   * @type string, uuid
   */
  teamId: string;
  /**
   * @type integer
   */
  teamMemberId: number;
  /**
   * @type string
   */
  title: string;
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    archive: boolean;
    /**
     * @type boolean
     */
    close: boolean;
    /**
     * @type boolean
     */
    duplicate: boolean;
    /**
     * @type boolean
     */
    edit: boolean;
    /**
     * @type boolean
     */
    publish: boolean;
    /**
     * @type boolean
     */
    restore: boolean;
  };
};
