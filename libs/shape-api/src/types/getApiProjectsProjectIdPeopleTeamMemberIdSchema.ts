/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamMemberSchema } from './teamMemberSchema';

export type GetApiProjectsProjectIdPeopleTeamMemberIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type integer
   */
  team_member_id: number;
};

/**
 * @description Team member
 */
export type GetApiProjectsProjectIdPeopleTeamMemberId200Schema = TeamMemberSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdPeopleTeamMemberId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdPeopleTeamMemberId403Schema = ErrorSchema;

/**
 * @description Team member not found
 */
export type GetApiProjectsProjectIdPeopleTeamMemberId404Schema = unknown;

export type GetApiProjectsProjectIdPeopleTeamMemberIdQueryResponseSchema =
  GetApiProjectsProjectIdPeopleTeamMemberId200Schema;

export type GetApiProjectsProjectIdPeopleTeamMemberIdSchemaQuery = {
  Response: GetApiProjectsProjectIdPeopleTeamMemberId200Schema;
  PathParams: GetApiProjectsProjectIdPeopleTeamMemberIdPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdPeopleTeamMemberId401Schema
    | GetApiProjectsProjectIdPeopleTeamMemberId403Schema
    | GetApiProjectsProjectIdPeopleTeamMemberId404Schema;
};
