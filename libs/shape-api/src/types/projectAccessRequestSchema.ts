/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { ProjectAccessRequestStatusSchema } from './projectAccessRequestStatusSchema';

export type ProjectAccessRequestSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  status: ProjectAccessRequestStatusSchema;
  /**
   * @type object
   */
  user: {
    /**
     * @type string, uuid
     */
    id: string;
    /**
     * @type string
     */
    name: string;
    /**
     * @type string
     */
    email: string;
    /**
     * @type string
     */
    avatarUrl: string | null;
  };
  /**
   * @type string, uuid
   */
  projectId: string;
  /**
   * @type object
   */
  team?: {
    /**
     * @type string, uuid
     */
    id: string;
    /**
     * @type string
     */
    displayName: string | null;
  } | null;
  /**
   * @type string
   */
  teamName: string;
  /**
   * @type string
   */
  teamAdminEmail: string | null;
  /**
   * @type string
   */
  message: string | null;
  /**
   * @type object
   */
  redirectedFromTeam: {
    /**
     * @type string, uuid
     */
    id: string;
    /**
     * @type string
     */
    displayName: string | null;
    /**
     * @type string
     */
    userName: string;
  } | null;
};
