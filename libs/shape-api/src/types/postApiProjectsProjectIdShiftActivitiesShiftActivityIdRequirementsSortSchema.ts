/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSortPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description Shift activity requirements sorted
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSort204Schema = unknown;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSort400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSort401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSort403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSort404Schema = unknown;

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSortMutationRequestSchema = {
  /**
   * @type array
   */
  requirement_ids: string[];
};

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSortMutationResponseSchema =
  PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSort204Schema;

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSortSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSort204Schema;
  Request: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSortMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSortPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSort400Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSort401Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSort403Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsSort404Schema;
};
