/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { OneOrManyIntegerSchema } from './oneOrManyIntegerSchema';
import type { ShiftReportListSchema } from './shiftReportListSchema';

export type GetApiProjectsProjectIdShiftReportsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type GetApiProjectsProjectIdShiftReportsQueryParamsSchema = {
  author_id?: OneOrManyIntegerSchema;
  /**
   * @type string | undefined, date
   */
  report_date_start?: string;
  /**
   * @type string | undefined, date
   */
  report_date_end?: string;
  /**
   * @type array | undefined
   */
  shift_report_ids?: string[];
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of published shift reports
 */
export type GetApiProjectsProjectIdShiftReports200Schema = ShiftReportListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdShiftReports400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftReports401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdShiftReports403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftReports404Schema = unknown;

export type GetApiProjectsProjectIdShiftReportsQueryResponseSchema = GetApiProjectsProjectIdShiftReports200Schema;

export type GetApiProjectsProjectIdShiftReportsSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftReports200Schema;
  PathParams: GetApiProjectsProjectIdShiftReportsPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdShiftReportsQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftReports400Schema
    | GetApiProjectsProjectIdShiftReports401Schema
    | GetApiProjectsProjectIdShiftReports403Schema
    | GetApiProjectsProjectIdShiftReports404Schema;
};
