/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftActivitySchema } from './shiftActivitySchema';

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestorePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description Shift activity restored
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestore200Schema = ShiftActivitySchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestore401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestore403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestore404Schema = unknown;

/**
 * @description Restore failed
 */
export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestore422Schema = ErrorSchema;

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestoreMutationResponseSchema =
  PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestore200Schema;

export type PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestoreSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestore200Schema;
  PathParams: PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestorePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestore401Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestore403Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestore404Schema
    | PostApiProjectsProjectIdShiftActivitiesShiftActivityIdRestore422Schema;
};
