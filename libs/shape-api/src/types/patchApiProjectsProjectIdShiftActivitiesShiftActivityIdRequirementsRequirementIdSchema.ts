/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftActivityRequirementSchema } from './shiftActivityRequirementSchema';

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
  /**
   * @type string, uuid
   */
  requirement_id: string;
};

/**
 * @description Shift activity requirement updated
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementId200Schema =
  ShiftActivityRequirementSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementId401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementId403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementId404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementId422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  title?: string;
};

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdMutationResponseSchema =
  PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementId200Schema;

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementId200Schema;
  Request: PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementId401Schema
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementId403Schema
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementId404Schema
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdRequirementsRequirementId422Schema;
};
