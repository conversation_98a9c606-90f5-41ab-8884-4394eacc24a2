/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const shiftActivityOverviewDailyProgressEntryProgressLogPartialEntryTypeEnum = {
  progress_log: 'progress_log',
} as const;

export type ShiftActivityOverviewDailyProgressEntryProgressLogPartialEntryTypeEnumSchema =
  (typeof shiftActivityOverviewDailyProgressEntryProgressLogPartialEntryTypeEnum)[keyof typeof shiftActivityOverviewDailyProgressEntryProgressLogPartialEntryTypeEnum];

export type ShiftActivityOverviewDailyProgressEntryProgressLogPartialSchema = {
  /**
   * @type string
   */
  entryType: ShiftActivityOverviewDailyProgressEntryProgressLogPartialEntryTypeEnumSchema;
  /**
   * @type string, uuid
   */
  progressLogId: string;
  /**
   * @type string, uuid
   */
  weeklyWorkPlanId: string | null;
};
