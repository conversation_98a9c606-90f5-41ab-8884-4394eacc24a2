/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const issueStateEnum = {
  assigned: 'assigned',
  assignment_rejected: 'assignment_rejected',
  assignment_requested: 'assignment_requested',
  completed: 'completed',
  draft: 'draft',
  in_progress: 'in_progress',
  resolved: 'resolved',
} as const;

export type IssueStateEnumSchema = (typeof issueStateEnum)[keyof typeof issueStateEnum];

export type IssueStateSchema = IssueStateEnumSchema;
