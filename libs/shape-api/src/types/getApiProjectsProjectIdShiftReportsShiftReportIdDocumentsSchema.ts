/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DocumentKindSchema } from './documentKindSchema';
import type { DocumentReferenceAndDocumentListSchema } from './documentReferenceAndDocumentListSchema';
import type { ErrorSchema } from './errorSchema';
import type { OneOrManyIntegerSchema } from './oneOrManyIntegerSchema';
import type { OneOrManyUuidNullableSchema } from './oneOrManyUuidNullableSchema';

export type GetApiProjectsProjectIdShiftReportsShiftReportIdDocumentsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

export const getApiProjectsProjectIdShiftReportsShiftReportIdDocumentsQueryParamsSortOrderEnum = {
  asc: 'asc',
  desc: 'desc',
} as const;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdDocumentsQueryParamsSortOrderEnumSchema =
  (typeof getApiProjectsProjectIdShiftReportsShiftReportIdDocumentsQueryParamsSortOrderEnum)[keyof typeof getApiProjectsProjectIdShiftReportsShiftReportIdDocumentsQueryParamsSortOrderEnum];

export type GetApiProjectsProjectIdShiftReportsShiftReportIdDocumentsQueryParamsSchema = {
  /**
   * @type string | undefined, date-time
   */
  created_end?: string;
  /**
   * @type string | undefined, date-time
   */
  created_start?: string;
  /**
   * @type string | undefined
   */
  kind?: DocumentKindSchema;
  location_id?: OneOrManyUuidNullableSchema;
  team_member_id?: OneOrManyIntegerSchema;
  /**
   * @description Generic search query
   * @type string | undefined
   */
  search?: string;
  /**
   * @description Order of the results
   * @type string | undefined
   */
  sort_order?: GetApiProjectsProjectIdShiftReportsShiftReportIdDocumentsQueryParamsSortOrderEnumSchema;
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of shift report documents
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdDocuments200Schema = DocumentReferenceAndDocumentListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdDocuments400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdDocuments401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdDocuments403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdDocuments404Schema = unknown;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdDocumentsQueryResponseSchema =
  GetApiProjectsProjectIdShiftReportsShiftReportIdDocuments200Schema;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdDocumentsSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftReportsShiftReportIdDocuments200Schema;
  PathParams: GetApiProjectsProjectIdShiftReportsShiftReportIdDocumentsPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdShiftReportsShiftReportIdDocumentsQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdDocuments400Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdDocuments401Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdDocuments403Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdDocuments404Schema;
};
