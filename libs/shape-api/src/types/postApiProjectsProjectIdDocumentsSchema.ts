/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DocumentSchema } from './documentSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiProjectsProjectIdDocumentsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Document created
 */
export type PostApiProjectsProjectIdDocuments201Schema = DocumentSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdDocuments400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdDocuments401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdDocuments403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdDocuments404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdDocuments422Schema = ErrorSchema;

export type PostApiProjectsProjectIdDocumentsMutationRequestSchema = {
  /**
   * @type string
   */
  caption?: string | null;
  /**
   * @type string, uuid
   */
  location_id?: string | null;
  /**
   * @description The signed id given by the direct upload method
   * @type string
   */
  signed_id: string;
};

export type PostApiProjectsProjectIdDocumentsMutationResponseSchema = PostApiProjectsProjectIdDocuments201Schema;

export type PostApiProjectsProjectIdDocumentsSchemaMutation = {
  Response: PostApiProjectsProjectIdDocuments201Schema;
  Request: PostApiProjectsProjectIdDocumentsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdDocumentsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdDocuments400Schema
    | PostApiProjectsProjectIdDocuments401Schema
    | PostApiProjectsProjectIdDocuments403Schema
    | PostApiProjectsProjectIdDocuments404Schema
    | PostApiProjectsProjectIdDocuments422Schema;
};
