/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiOrgsOrgIdResendVerificationEmailPathParamsSchema = {
  /**
   * @type string, uuid
   */
  org_id: string;
};

/**
 * @description Email sent
 */
export type PostApiOrgsOrgIdResendVerificationEmail204Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiOrgsOrgIdResendVerificationEmail401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type PostApiOrgsOrgIdResendVerificationEmail403Schema = ErrorSchema;

/**
 * @description Org not found
 */
export type PostApiOrgsOrgIdResendVerificationEmail404Schema = unknown;

export type PostApiOrgsOrgIdResendVerificationEmailMutationResponseSchema =
  PostApiOrgsOrgIdResendVerificationEmail204Schema;

export type PostApiOrgsOrgIdResendVerificationEmailSchemaMutation = {
  Response: PostApiOrgsOrgIdResendVerificationEmail204Schema;
  PathParams: PostApiOrgsOrgIdResendVerificationEmailPathParamsSchema;
  Errors:
    | PostApiOrgsOrgIdResendVerificationEmail401Schema
    | PostApiOrgsOrgIdResendVerificationEmail403Schema
    | PostApiOrgsOrgIdResendVerificationEmail404Schema;
};
