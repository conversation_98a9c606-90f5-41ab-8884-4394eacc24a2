/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiProjectsProjectIdDisciplinesSortPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Disciplines sorted
 */
export type PostApiProjectsProjectIdDisciplinesSort204Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdDisciplinesSort401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdDisciplinesSort403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdDisciplinesSort404Schema = unknown;

export type PostApiProjectsProjectIdDisciplinesSortMutationRequestSchema = {
  /**
   * @type array | undefined
   */
  ordered_discipline_ids?: string[];
};

export type PostApiProjectsProjectIdDisciplinesSortMutationResponseSchema =
  PostApiProjectsProjectIdDisciplinesSort204Schema;

export type PostApiProjectsProjectIdDisciplinesSortSchemaMutation = {
  Response: PostApiProjectsProjectIdDisciplinesSort204Schema;
  Request: PostApiProjectsProjectIdDisciplinesSortMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdDisciplinesSortPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdDisciplinesSort401Schema
    | PostApiProjectsProjectIdDisciplinesSort403Schema
    | PostApiProjectsProjectIdDisciplinesSort404Schema;
};
