/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { OneOrManyUuidSchema } from './oneOrManyUuidSchema';
import type { TeamMemberConstructionRoleSchema } from './teamMemberConstructionRoleSchema';
import type { TeamMemberListSchema } from './teamMemberListSchema';
import type { TeamMemberRoleSchema } from './teamMemberRoleSchema';
import type { TeamMemberStatusSchema } from './teamMemberStatusSchema';

export type GetApiProjectsProjectIdPeoplePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type GetApiProjectsProjectIdPeopleQueryParamsSchema = {
  /**
   * @type string | undefined, uuid
   */
  accessible_issue_id?: string;
  construction_role?: TeamMemberConstructionRoleSchema[] | TeamMemberConstructionRoleSchema;
  role?: TeamMemberRoleSchema[] | TeamMemberRoleSchema;
  status?: TeamMemberStatusSchema[] | TeamMemberStatusSchema;
  team_id?: OneOrManyUuidSchema;
  /**
   * @type string | undefined
   */
  search?: string;
};

/**
 * @description Project people
 */
export type GetApiProjectsProjectIdPeople200Schema = TeamMemberListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdPeople400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdPeople401Schema = AuthenticationErrorSchema;

/**
 * @description Project not found
 */
export type GetApiProjectsProjectIdPeople404Schema = unknown;

export type GetApiProjectsProjectIdPeopleQueryResponseSchema = GetApiProjectsProjectIdPeople200Schema;

export type GetApiProjectsProjectIdPeopleSchemaQuery = {
  Response: GetApiProjectsProjectIdPeople200Schema;
  PathParams: GetApiProjectsProjectIdPeoplePathParamsSchema;
  QueryParams: GetApiProjectsProjectIdPeopleQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdPeople400Schema
    | GetApiProjectsProjectIdPeople401Schema
    | GetApiProjectsProjectIdPeople404Schema;
};
