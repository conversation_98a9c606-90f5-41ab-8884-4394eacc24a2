/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { OneOrManyIntegerSchema } from './oneOrManyIntegerSchema';
import type { ShiftReportListSchema } from './shiftReportListSchema';

export type GetApiProjectsProjectIdShiftReportsArchivedPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type GetApiProjectsProjectIdShiftReportsArchivedQueryParamsSchema = {
  author_id?: OneOrManyIntegerSchema;
  /**
   * @type string | undefined, date
   */
  report_date_start?: string;
  /**
   * @type string | undefined, date
   */
  report_date_end?: string;
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of archived shift reports
 */
export type GetApiProjectsProjectIdShiftReportsArchived200Schema = ShiftReportListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdShiftReportsArchived400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftReportsArchived401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdShiftReportsArchived403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftReportsArchived404Schema = unknown;

export type GetApiProjectsProjectIdShiftReportsArchivedQueryResponseSchema =
  GetApiProjectsProjectIdShiftReportsArchived200Schema;

export type GetApiProjectsProjectIdShiftReportsArchivedSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftReportsArchived200Schema;
  PathParams: GetApiProjectsProjectIdShiftReportsArchivedPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdShiftReportsArchivedQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftReportsArchived400Schema
    | GetApiProjectsProjectIdShiftReportsArchived401Schema
    | GetApiProjectsProjectIdShiftReportsArchived403Schema
    | GetApiProjectsProjectIdShiftReportsArchived404Schema;
};
