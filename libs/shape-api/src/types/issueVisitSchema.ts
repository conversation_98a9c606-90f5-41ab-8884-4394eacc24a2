/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export type IssueVisitSchema = {
  /**
   * @type integer
   */
  previouslyUnreadPublicUpdatesCount: number;
  /**
   * @type integer
   */
  previouslyUnreadTeamUpdatesCount: number;
  /**
   * @type string, date-time
   */
  previouslyVisitedAt: string | null;
  /**
   * @type string, date-time
   */
  visitedAt: string | null;
};
