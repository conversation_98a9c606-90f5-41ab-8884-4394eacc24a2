/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ProjectListSchema } from './projectListSchema';

/**
 * @description List of projects
 */
export type GetApiProjects200Schema = ProjectListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjects401Schema = AuthenticationErrorSchema;

export type GetApiProjectsQueryResponseSchema = GetApiProjects200Schema;

export type GetApiProjectsSchemaQuery = {
  Response: GetApiProjects200Schema;
  Errors: GetApiProjects401Schema;
};
