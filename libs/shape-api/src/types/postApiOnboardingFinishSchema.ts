/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamMemberConstructionRoleSchema } from './teamMemberConstructionRoleSchema';
import type { TeamMemberRoleSchema } from './teamMemberRoleSchema';
import type { UserOnboardingSchema } from './userOnboardingSchema';

/**
 * @description User onboarding finished
 */
export type PostApiOnboardingFinish200Schema = UserOnboardingSchema;

/**
 * @description Authentication required
 */
export type PostApiOnboardingFinish401Schema = AuthenticationErrorSchema;

/**
 * @description User onboarding not found
 */
export type PostApiOnboardingFinish404Schema = unknown;

/**
 * @description Finish failed
 */
export type PostApiOnboardingFinish422Schema = ErrorSchema;

export const userDefaultProductEnum2 = {
  issues: 'issues',
  shift_manager: 'shift_manager',
  channels: 'channels',
} as const;

export type UserDefaultProductEnum2Schema = (typeof userDefaultProductEnum2)[keyof typeof userDefaultProductEnum2];

export type PostApiOnboardingFinishMutationRequestSchema = {
  /**
   * @type object | undefined
   */
  user?: {
    construction_role?: TeamMemberConstructionRoleSchema | null;
    /**
     * @type boolean
     */
    used_shape_before?: boolean | null;
    /**
     * @type string
     */
    hear_about_shape?: string | null;
    default_product?: UserDefaultProductEnum2Schema | null;
  };
  /**
   * @type object | undefined
   */
  project?: {
    /**
     * @type string | undefined
     */
    title?: string;
    /**
     * @type string | undefined
     */
    short_name?: string;
    /**
     * @type string | undefined, uuid
     */
    project_logo?: string;
    /**
     * @type string | undefined
     */
    timezone?: string;
  };
  /**
   * @type object | undefined
   */
  team?: {
    /**
     * @type string | undefined
     */
    display_name?: string;
    /**
     * @type array | undefined
     */
    members?: {
      /**
       * @type string | undefined
       */
      email?: string;
      /**
       * @type string | undefined
       */
      role?: TeamMemberRoleSchema;
      /**
       * @type string | undefined
       */
      construction_role?: TeamMemberConstructionRoleSchema;
    }[];
  };
};

export type PostApiOnboardingFinishMutationResponseSchema = PostApiOnboardingFinish200Schema;

export type PostApiOnboardingFinishSchemaMutation = {
  Response: PostApiOnboardingFinish200Schema;
  Request: PostApiOnboardingFinishMutationRequestSchema;
  Errors: PostApiOnboardingFinish401Schema | PostApiOnboardingFinish404Schema | PostApiOnboardingFinish422Schema;
};
