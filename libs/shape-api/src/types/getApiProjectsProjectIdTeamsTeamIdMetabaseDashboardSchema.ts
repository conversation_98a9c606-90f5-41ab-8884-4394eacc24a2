/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { MetabaseDashboardSchema } from './metabaseDashboardSchema';

export type GetApiProjectsProjectIdTeamsTeamIdMetabaseDashboardPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
};

/**
 * @description Dashboard token
 */
export type GetApiProjectsProjectIdTeamsTeamIdMetabaseDashboard200Schema = MetabaseDashboardSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdTeamsTeamIdMetabaseDashboard401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdTeamsTeamIdMetabaseDashboard403Schema = ErrorSchema;

/**
 * @description Metabase not configured
 */
export type GetApiProjectsProjectIdTeamsTeamIdMetabaseDashboard503Schema = unknown;

export type GetApiProjectsProjectIdTeamsTeamIdMetabaseDashboardQueryResponseSchema =
  GetApiProjectsProjectIdTeamsTeamIdMetabaseDashboard200Schema;

export type GetApiProjectsProjectIdTeamsTeamIdMetabaseDashboardSchemaQuery = {
  Response: GetApiProjectsProjectIdTeamsTeamIdMetabaseDashboard200Schema;
  PathParams: GetApiProjectsProjectIdTeamsTeamIdMetabaseDashboardPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdTeamsTeamIdMetabaseDashboard401Schema
    | GetApiProjectsProjectIdTeamsTeamIdMetabaseDashboard403Schema
    | GetApiProjectsProjectIdTeamsTeamIdMetabaseDashboard503Schema;
};
