/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export type LocationSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string, uuid
   */
  projectId: string;
  /**
   * @type string
   */
  name: string;
  /**
   * @type string
   */
  shortCode: string;
  /**
   * @type integer
   */
  sortPosition: number;
  /**
   * @type string, uuid
   */
  parentLocationId: string | null;
  /**
   * @type string
   */
  nodePath: string;
};
