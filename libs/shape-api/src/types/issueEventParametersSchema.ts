/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueEventParametersAcceptAssignmentSchema } from './issueEventParametersAcceptAssignmentSchema';
import type { IssueEventParametersAddApproverSchema } from './issueEventParametersAddApproverSchema';
import type { IssueEventParametersAddTeamSchema } from './issueEventParametersAddTeamSchema';
import type { IssueEventParametersApproveSchema } from './issueEventParametersApproveSchema';
import type { IssueEventParametersArchiveSchema } from './issueEventParametersArchiveSchema';
import type { IssueEventParametersAssignSchema } from './issueEventParametersAssignSchema';
import type { IssueEventParametersChangeStatusSchema } from './issueEventParametersChangeStatusSchema';
import type { IssueEventParametersCommentOnSchema } from './issueEventParametersCommentOnSchema';
import type { IssueEventParametersCreateSchema } from './issueEventParametersCreateSchema';
import type { IssueEventParametersDeleteDocumentSchema } from './issueEventParametersDeleteDocumentSchema';
import type { IssueEventParametersDeleteImageSchema } from './issueEventParametersDeleteImageSchema';
import type { IssueEventParametersDeleteStatusStatementSchema } from './issueEventParametersDeleteStatusStatementSchema';
import type { IssueEventParametersPrivateCommentOnSchema } from './issueEventParametersPrivateCommentOnSchema';
import type { IssueEventParametersRejectAssignmentSchema } from './issueEventParametersRejectAssignmentSchema';
import type { IssueEventParametersRejectResolutionSchema } from './issueEventParametersRejectResolutionSchema';
import type { IssueEventParametersRemoveApproverSchema } from './issueEventParametersRemoveApproverSchema';
import type { IssueEventParametersRemoveTeamSchema } from './issueEventParametersRemoveTeamSchema';
import type { IssueEventParametersReopenSchema } from './issueEventParametersReopenSchema';
import type { IssueEventParametersRestoreSchema } from './issueEventParametersRestoreSchema';
import type { IssueEventParametersUpdateImageSchema } from './issueEventParametersUpdateImageSchema';
import type { IssueEventParametersUpdateImpactSchema } from './issueEventParametersUpdateImpactSchema';
import type { IssueEventParametersUpdateObserverSchema } from './issueEventParametersUpdateObserverSchema';
import type { IssueEventParametersUpdateSchema } from './issueEventParametersUpdateSchema';
import type { IssueEventParametersUpdateStatusStatementSchema } from './issueEventParametersUpdateStatusStatementSchema';
import type { IssueEventParametersUploadDocumentSchema } from './issueEventParametersUploadDocumentSchema';
import type { IssueEventParametersUploadImageSchema } from './issueEventParametersUploadImageSchema';

export type IssueEventParametersSchema =
  | (IssueEventParametersAcceptAssignmentSchema & {
      eventType: 'accept_assignment';
    })
  | (IssueEventParametersAddApproverSchema & {
      eventType: 'add_approver';
    })
  | (IssueEventParametersAddTeamSchema & {
      eventType: 'add_team';
    })
  | (IssueEventParametersApproveSchema & {
      eventType: 'approve';
    })
  | (IssueEventParametersArchiveSchema & {
      eventType: 'archive';
    })
  | (IssueEventParametersAssignSchema & {
      eventType: 'assign';
    })
  | (IssueEventParametersChangeStatusSchema & {
      eventType: 'change_status';
    })
  | (IssueEventParametersCommentOnSchema & {
      eventType: 'comment_on';
    })
  | (IssueEventParametersCreateSchema & {
      eventType: 'create';
    })
  | (IssueEventParametersDeleteDocumentSchema & {
      eventType: 'delete_document';
    })
  | (IssueEventParametersDeleteImageSchema & {
      eventType: 'delete_image';
    })
  | (IssueEventParametersDeleteStatusStatementSchema & {
      eventType: 'delete_status_statement';
    })
  | (IssueEventParametersPrivateCommentOnSchema & {
      eventType: 'private_comment_on';
    })
  | (IssueEventParametersRejectAssignmentSchema & {
      eventType: 'reject_assignment';
    })
  | (IssueEventParametersRejectResolutionSchema & {
      eventType: 'reject_resolution';
    })
  | (IssueEventParametersRemoveApproverSchema & {
      eventType: 'remove_approver';
    })
  | (IssueEventParametersRemoveTeamSchema & {
      eventType: 'remove_team';
    })
  | (IssueEventParametersReopenSchema & {
      eventType: 'reopen';
    })
  | (IssueEventParametersRestoreSchema & {
      eventType: 'restore';
    })
  | (IssueEventParametersUpdateSchema & {
      eventType: 'update';
    })
  | (IssueEventParametersUpdateImageSchema & {
      eventType: 'update_image';
    })
  | (IssueEventParametersUpdateImpactSchema & {
      eventType: 'update_impact';
    })
  | (IssueEventParametersUpdateObserverSchema & {
      eventType: 'update_observer';
    })
  | (IssueEventParametersUpdateStatusStatementSchema & {
      eventType: 'update_status_statement';
    })
  | (IssueEventParametersUploadDocumentSchema & {
      eventType: 'upload_document';
    })
  | (IssueEventParametersUploadImageSchema & {
      eventType: 'upload_image';
    });
