/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { QueuedTaskSchema } from './queuedTaskSchema';

export type PostApiProjectsProjectIdIssuesSmartIssuesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Request accepted
 */
export type PostApiProjectsProjectIdIssuesSmartIssues202Schema = QueuedTaskSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesSmartIssues401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesSmartIssues403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdIssuesSmartIssues404Schema = unknown;

export type PostApiProjectsProjectIdIssuesSmartIssuesMutationRequestSchema = {
  /**
   * @type string
   */
  content: string;
  /**
   * @type array | undefined
   */
  image_document_ids?: string[];
};

export type PostApiProjectsProjectIdIssuesSmartIssuesMutationResponseSchema =
  PostApiProjectsProjectIdIssuesSmartIssues202Schema;

export type PostApiProjectsProjectIdIssuesSmartIssuesSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesSmartIssues202Schema;
  Request: PostApiProjectsProjectIdIssuesSmartIssuesMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdIssuesSmartIssuesPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesSmartIssues401Schema
    | PostApiProjectsProjectIdIssuesSmartIssues403Schema
    | PostApiProjectsProjectIdIssuesSmartIssues404Schema;
};
