/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { QueuedTaskSchema } from './queuedTaskSchema';

export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExportPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  potential_change_id: string;
};

/**
 * @description Export created
 */
export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExport202Schema = QueuedTaskSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExport401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExport403Schema = ErrorSchema;

/**
 * @description Potential change not found
 */
export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExport404Schema = unknown;

export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExportMutationResponseSchema =
  PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExport202Schema;

export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExportSchemaMutation = {
  Response: PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExport202Schema;
  PathParams: PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExportPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExport401Schema
    | PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExport403Schema
    | PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdExport404Schema;
};
