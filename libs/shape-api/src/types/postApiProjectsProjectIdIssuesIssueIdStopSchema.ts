/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueSchema } from './issueSchema';

export type PostApiProjectsProjectIdIssuesIssueIdStopPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue updated
 */
export type PostApiProjectsProjectIdIssuesIssueIdStop200Schema = IssueSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdIssuesIssueIdStop400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdStop401Schema = AuthenticationErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdStop404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdStopMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdStop200Schema;

export type PostApiProjectsProjectIdIssuesIssueIdStopSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdStop200Schema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdStopPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdStop400Schema
    | PostApiProjectsProjectIdIssuesIssueIdStop401Schema
    | PostApiProjectsProjectIdIssuesIssueIdStop404Schema;
};
