/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { QueuedTaskListSchema } from './queuedTaskListSchema';

export type GetApiQueuedTasksQueryParamsSchema = {
  /**
   * @type array
   */
  id: string[];
};

/**
 * @description List of queued tasks
 */
export type GetApiQueuedTasks200Schema = QueuedTaskListSchema;

/**
 * @description Bad request
 */
export type GetApiQueuedTasks400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiQueuedTasks401Schema = AuthenticationErrorSchema;

export type GetApiQueuedTasksQueryResponseSchema = GetApiQueuedTasks200Schema;

export type GetApiQueuedTasksSchemaQuery = {
  Response: GetApiQueuedTasks200Schema;
  QueryParams: GetApiQueuedTasksQueryParamsSchema;
  Errors: GetApiQueuedTasks400Schema | GetApiQueuedTasks401Schema;
};
