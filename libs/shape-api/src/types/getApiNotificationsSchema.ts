/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { NotificationListSchema } from './notificationListSchema';

export type GetApiNotificationsQueryParamsSchema = {
  /**
   * @description Page number
   * @type integer | undefined
   */
  page?: number;
};

/**
 * @description List of notifications
 */
export type GetApiNotifications200Schema = NotificationListSchema;

/**
 * @description Bad request
 */
export type GetApiNotifications400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiNotifications401Schema = AuthenticationErrorSchema;

export type GetApiNotificationsQueryResponseSchema = GetApiNotifications200Schema;

export type GetApiNotificationsSchemaQuery = {
  Response: GetApiNotifications200Schema;
  QueryParams: GetApiNotificationsQueryParamsSchema;
  Errors: GetApiNotifications400Schema | GetApiNotifications401Schema;
};
