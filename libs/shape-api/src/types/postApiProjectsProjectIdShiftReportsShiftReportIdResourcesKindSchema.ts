/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ResourceKindSchema } from './resourceKindSchema';
import type { ResourceSchema } from './resourceSchema';

export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
  /**
   * @type string
   */
  kind: ResourceKindSchema;
};

/**
 * @description Resource created
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind201Schema = ResourceSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind403Schema = unknown;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind422Schema = ErrorSchema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMutationRequestSchema = {
  /**
   * @type string
   */
  name: string;
};

export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMutationResponseSchema =
  PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind201Schema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind201Schema;
  Request: PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKindPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind401Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind403Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind404Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdResourcesKind422Schema;
};
