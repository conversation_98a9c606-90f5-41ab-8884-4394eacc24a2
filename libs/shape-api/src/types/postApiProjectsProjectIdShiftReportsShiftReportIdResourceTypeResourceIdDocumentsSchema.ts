/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DocumentReferenceAndDocumentSchema } from './documentReferenceAndDocumentSchema';
import type { ErrorSchema } from './errorSchema';
import type { NewOrExistingDocumentWithAttributesBodyParameterSchema } from './newOrExistingDocumentWithAttributesBodyParameterSchema';

export const postApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsPathParamsResourceTypeEnum =
  {
    activities: 'activities',
    contract_forces: 'contract_forces',
    down_times: 'down_times',
    equipments: 'equipments',
    materials: 'materials',
    safety_health_environments: 'safety_health_environments',
  } as const;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsPathParamsResourceTypeEnumSchema =
  (typeof postApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsPathParamsResourceTypeEnum)[keyof typeof postApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsPathParamsResourceTypeEnum];

export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
  /**
   * @type string
   */
  resource_type: PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsPathParamsResourceTypeEnumSchema;
  /**
   * @type string, uuid
   */
  resource_id: string;
};

/**
 * @description Document added
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments201Schema =
  DocumentReferenceAndDocumentSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments404Schema = unknown;

/**
 * @description Invalid document
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments422Schema = ErrorSchema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsMutationRequestSchema =
  NewOrExistingDocumentWithAttributesBodyParameterSchema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsMutationResponseSchema =
  PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments201Schema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments201Schema;
  Request: PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocumentsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments400Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments401Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments403Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments404Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdResourceTypeResourceIdDocuments422Schema;
};
