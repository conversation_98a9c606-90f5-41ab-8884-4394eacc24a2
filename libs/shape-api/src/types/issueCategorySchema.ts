/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export const issueCategoryEnum = {
  'close out': 'close out',
  'something needed': 'something needed',
  safety: 'safety',
  progress: 'progress',
} as const;

export type IssueCategoryEnumSchema = (typeof issueCategoryEnum)[keyof typeof issueCategoryEnum];

export type IssueCategorySchema = IssueCategoryEnumSchema;
