/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersYourIssueApprovalRequestWasAcceptedTypeEnum = {
  your_issue_approval_request_was_accepted: 'your_issue_approval_request_was_accepted',
} as const;

export type NotificationParametersYourIssueApprovalRequestWasAcceptedTypeEnumSchema =
  (typeof notificationParametersYourIssueApprovalRequestWasAcceptedTypeEnum)[keyof typeof notificationParametersYourIssueApprovalRequestWasAcceptedTypeEnum];

export type NotificationParametersYourIssueApprovalRequestWasAcceptedSchema = {
  /**
   * @type string
   */
  type: NotificationParametersYourIssueApprovalRequestWasAcceptedTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type string
     */
    progress: string;
    /**
     * @type string, uuid
     */
    projectId: string;
  };
};
