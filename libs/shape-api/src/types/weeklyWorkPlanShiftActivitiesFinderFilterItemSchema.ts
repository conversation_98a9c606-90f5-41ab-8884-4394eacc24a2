/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { WeeklyWorkPlanShiftActivitiesFinderFilterItemValueSchema } from './weeklyWorkPlanShiftActivitiesFinderFilterItemValueSchema';

export const weeklyWorkPlanShiftActivitiesFinderFilterItemNameEnum = {
  actual_end_date_end: 'actual_end_date_end',
  actual_end_date_relative: 'actual_end_date_relative',
  actual_end_date_start: 'actual_end_date_start',
  actual_start_date_end: 'actual_start_date_end',
  actual_start_date_relative: 'actual_start_date_relative',
  actual_start_date_start: 'actual_start_date_start',
  archived: 'archived',
  assigned_team_member_id: 'assigned_team_member_id',
  critical: 'critical',
  expected_finish_date_end: 'expected_finish_date_end',
  expected_finish_date_relative: 'expected_finish_date_relative',
  expected_finish_date_start: 'expected_finish_date_start',
  id: 'id',
  location_id: 'location_id',
  organisation_resource_id: 'organisation_resource_id',
  owner_id: 'owner_id',
  planned_end_date_end: 'planned_end_date_end',
  planned_end_date_relative: 'planned_end_date_relative',
  planned_end_date_start: 'planned_end_date_start',
  planned_start_date_end: 'planned_start_date_end',
  planned_start_date_relative: 'planned_start_date_relative',
  planned_start_date_start: 'planned_start_date_start',
  ready: 'ready',
  search: 'search',
  status: 'status',
} as const;

export type WeeklyWorkPlanShiftActivitiesFinderFilterItemNameEnumSchema =
  (typeof weeklyWorkPlanShiftActivitiesFinderFilterItemNameEnum)[keyof typeof weeklyWorkPlanShiftActivitiesFinderFilterItemNameEnum];

export type WeeklyWorkPlanShiftActivitiesFinderFilterItemSchema = {
  /**
   * @type string
   */
  name: WeeklyWorkPlanShiftActivitiesFinderFilterItemNameEnumSchema;
  value:
    | WeeklyWorkPlanShiftActivitiesFinderFilterItemValueSchema
    | WeeklyWorkPlanShiftActivitiesFinderFilterItemValueSchema[];
};
