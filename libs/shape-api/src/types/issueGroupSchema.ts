/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const issueGroupEnum = {
  category: 'category',
  custom_field: 'custom_field',
  discipline: 'discipline',
  impact: 'impact',
  location: 'location',
  observer: 'observer',
  observer_team: 'observer_team',
  responsible: 'responsible',
  responsible_team: 'responsible_team',
  state: 'state',
  sub_category: 'sub_category',
  team_issues: 'team_issues',
  user_issues: 'user_issues',
} as const;

export type IssueGroupEnumSchema = (typeof issueGroupEnum)[keyof typeof issueGroupEnum];

export type IssueGroupSchema = IssueGroupEnumSchema;
