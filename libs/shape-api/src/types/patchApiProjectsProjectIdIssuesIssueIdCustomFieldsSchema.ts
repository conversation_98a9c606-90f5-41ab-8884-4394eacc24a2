/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueCustomFieldListSchema } from './issueCustomFieldListSchema';

export type PatchApiProjectsProjectIdIssuesIssueIdCustomFieldsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Custom fields updated
 */
export type PatchApiProjectsProjectIdIssuesIssueIdCustomFields200Schema = IssueCustomFieldListSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdIssuesIssueIdCustomFields401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdIssuesIssueIdCustomFields403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PatchApiProjectsProjectIdIssuesIssueIdCustomFields404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdIssuesIssueIdCustomFields422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdIssuesIssueIdCustomFieldsMutationRequestSchema = {
  /**
   * @type string, uuid
   */
  custom_field_id: string;
  /**
   * @type string
   */
  value: string | null;
}[];

export type PatchApiProjectsProjectIdIssuesIssueIdCustomFieldsMutationResponseSchema =
  PatchApiProjectsProjectIdIssuesIssueIdCustomFields200Schema;

export type PatchApiProjectsProjectIdIssuesIssueIdCustomFieldsSchemaMutation = {
  Response: PatchApiProjectsProjectIdIssuesIssueIdCustomFields200Schema;
  Request: PatchApiProjectsProjectIdIssuesIssueIdCustomFieldsMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdIssuesIssueIdCustomFieldsPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdIssuesIssueIdCustomFields401Schema
    | PatchApiProjectsProjectIdIssuesIssueIdCustomFields403Schema
    | PatchApiProjectsProjectIdIssuesIssueIdCustomFields404Schema
    | PatchApiProjectsProjectIdIssuesIssueIdCustomFields422Schema;
};
