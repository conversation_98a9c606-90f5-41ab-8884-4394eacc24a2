/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { WeeklyWorkPlanSchema } from './weeklyWorkPlanSchema';

export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

/**
 * @description Weekly Work Plan
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId200Schema = WeeklyWorkPlanSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId403Schema = unknown;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId404Schema = unknown;

export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdQueryResponseSchema =
  GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId200Schema;

export type GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdSchemaQuery = {
  Response: GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId200Schema;
  PathParams: GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId401Schema
    | GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId403Schema
    | GetApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId404Schema;
};
