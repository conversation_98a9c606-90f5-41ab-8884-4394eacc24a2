/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { QueuedTaskSchema } from './queuedTaskSchema';

export type PostApiProjectsProjectIdShiftReportsShiftReportIdExportPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

export const postApiProjectsProjectIdShiftReportsShiftReportIdExportQueryParamsFileTypeEnum = {
  pdf: 'pdf',
  csv: 'csv',
  xlsx: 'xlsx',
} as const;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdExportQueryParamsFileTypeEnumSchema =
  (typeof postApiProjectsProjectIdShiftReportsShiftReportIdExportQueryParamsFileTypeEnum)[keyof typeof postApiProjectsProjectIdShiftReportsShiftReportIdExportQueryParamsFileTypeEnum];

export type PostApiProjectsProjectIdShiftReportsShiftReportIdExportQueryParamsSchema = {
  /**
   * @type string
   */
  file_type: PostApiProjectsProjectIdShiftReportsShiftReportIdExportQueryParamsFileTypeEnumSchema;
};

/**
 * @description Request accepted
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdExport202Schema = QueuedTaskSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdExport400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdExport401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdExport403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdExport404Schema = unknown;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdExportMutationResponseSchema =
  PostApiProjectsProjectIdShiftReportsShiftReportIdExport202Schema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdExportSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftReportsShiftReportIdExport202Schema;
  PathParams: PostApiProjectsProjectIdShiftReportsShiftReportIdExportPathParamsSchema;
  QueryParams: PostApiProjectsProjectIdShiftReportsShiftReportIdExportQueryParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdExport400Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdExport401Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdExport403Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdExport404Schema;
};
