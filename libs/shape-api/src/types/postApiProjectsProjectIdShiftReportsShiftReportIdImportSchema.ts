/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftReportSchema } from './shiftReportSchema';

export type PostApiProjectsProjectIdShiftReportsShiftReportIdImportPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

/**
 * @description Shift report updated
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdImport200Schema = ShiftReportSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdImport400Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdImport401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdImport403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdImport404Schema = unknown;

/**
 * @description Update failed
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdImport422Schema = ErrorSchema;

export const postApiProjectsProjectIdShiftReportsShiftReportIdImportMutationRequestSectionsEnum = {
  contract_forces: 'contract_forces',
  down_times: 'down_times',
} as const;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdImportMutationRequestSectionsEnumSchema =
  (typeof postApiProjectsProjectIdShiftReportsShiftReportIdImportMutationRequestSectionsEnum)[keyof typeof postApiProjectsProjectIdShiftReportsShiftReportIdImportMutationRequestSectionsEnum];

export type PostApiProjectsProjectIdShiftReportsShiftReportIdImportMutationRequestSchema = {
  /**
   * @description The shift report sections to import
   * @type array | undefined
   */
  sections?: PostApiProjectsProjectIdShiftReportsShiftReportIdImportMutationRequestSectionsEnumSchema[];
  /**
   * @description The XLSX file
   * @type string | undefined, binary
   */
  xlsx?: Blob;
};

export type PostApiProjectsProjectIdShiftReportsShiftReportIdImportMutationResponseSchema =
  PostApiProjectsProjectIdShiftReportsShiftReportIdImport200Schema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdImportSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftReportsShiftReportIdImport200Schema;
  Request: PostApiProjectsProjectIdShiftReportsShiftReportIdImportMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftReportsShiftReportIdImportPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdImport400Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdImport401Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdImport403Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdImport404Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdImport422Schema;
};
