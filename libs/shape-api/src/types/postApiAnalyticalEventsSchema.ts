/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';

/**
 * @description Analytical event tracked
 */
export type PostApiAnalyticalEvents202Schema = unknown;

/**
 * @description Bad request
 */
export type PostApiAnalyticalEvents400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiAnalyticalEvents401Schema = AuthenticationErrorSchema;

export type PostApiAnalyticalEventsMutationRequestSchema = {
  /**
   * @type string
   */
  event_name: string;
  /**
   * @type object | undefined
   */
  properties?: object;
};

export type PostApiAnalyticalEventsMutationResponseSchema = PostApiAnalyticalEvents202Schema;

export type PostApiAnalyticalEventsSchemaMutation = {
  Response: PostApiAnalyticalEvents202Schema;
  Request: PostApiAnalyticalEventsMutationRequestSchema;
  Errors: PostApiAnalyticalEvents400Schema | PostApiAnalyticalEvents401Schema;
};
