/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueSchema } from './issueSchema';

export type PostApiProjectsProjectIdIssuesIssueIdSubmitPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue submitted
 */
export type PostApiProjectsProjectIdIssuesIssueIdSubmit200Schema = IssueSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdSubmit401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesIssueIdSubmit403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdSubmit404Schema = unknown;

/**
 * @description Issue not submittable
 */
export type PostApiProjectsProjectIdIssuesIssueIdSubmit422Schema = ErrorSchema;

export type PostApiProjectsProjectIdIssuesIssueIdSubmitMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdSubmit200Schema;

export type PostApiProjectsProjectIdIssuesIssueIdSubmitSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdSubmit200Schema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdSubmitPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdSubmit401Schema
    | PostApiProjectsProjectIdIssuesIssueIdSubmit403Schema
    | PostApiProjectsProjectIdIssuesIssueIdSubmit404Schema
    | PostApiProjectsProjectIdIssuesIssueIdSubmit422Schema;
};
