/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { WeeklyWorkPlanSchema } from './weeklyWorkPlanSchema';

export type PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

/**
 * @description Weekly work plan updated
 */
export type PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId200Schema = WeeklyWorkPlanSchema;

/**
 * @description Bad request
 */
export type PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId404Schema = unknown;

/**
 * @description Update failed
 */
export type PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId422Schema = ErrorSchema;

export type PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdMutationRequestSchema = {
  /**
   * @type string | undefined, date
   */
  end_date?: string;
  /**
   * @type string | undefined, date
   */
  start_date?: string;
  /**
   * @type string | undefined
   */
  title?: string;
};

export type PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdMutationResponseSchema =
  PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId200Schema;

export type PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdSchemaMutation = {
  Response: PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId200Schema;
  Request: PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdMutationRequestSchema;
  PathParams: PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPathParamsSchema;
  Errors:
    | PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId400Schema
    | PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId401Schema
    | PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId403Schema
    | PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId404Schema
    | PutApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanId422Schema;
};
