/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueSchema } from './issueSchema';

export type PostApiProjectsProjectIdIssuesIssueIdReopenPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue reopened
 */
export type PostApiProjectsProjectIdIssuesIssueIdReopen200Schema = IssueSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdIssuesIssueIdReopen400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdReopen401Schema = AuthenticationErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdReopen404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdReopenMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdReopen200Schema;

export type PostApiProjectsProjectIdIssuesIssueIdReopenSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdReopen200Schema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdReopenPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdReopen400Schema
    | PostApiProjectsProjectIdIssuesIssueIdReopen401Schema
    | PostApiProjectsProjectIdIssuesIssueIdReopen404Schema;
};
