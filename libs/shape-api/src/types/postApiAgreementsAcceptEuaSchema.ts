/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export type PostApiAgreementsAcceptEuaQueryParamsSchema = {
  /**
   * @type string | undefined, uuid
   */
  agreement_id?: string;
};

/**
 * @description Accepts End User Agreement
 */
export type PostApiAgreementsAcceptEua204Schema = unknown;

/**
 * @description Not an End User agreement
 */
export type PostApiAgreementsAcceptEua400Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiAgreementsAcceptEua401Schema = unknown;

export type PostApiAgreementsAcceptEuaMutationResponseSchema = PostApiAgreementsAcceptEua204Schema;

export type PostApiAgreementsAcceptEuaSchemaMutation = {
  Response: PostApiAgreementsAcceptEua204Schema;
  QueryParams: PostApiAgreementsAcceptEuaQueryParamsSchema;
  Errors: PostApiAgreementsAcceptEua400Schema | PostApiAgreementsAcceptEua401Schema;
};
