/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DisciplineSchema } from './disciplineSchema';
import type { ErrorSchema } from './errorSchema';

export type GetApiProjectsProjectIdDisciplinesDisciplineIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  discipline_id: string;
};

/**
 * @description Discipline
 */
export type GetApiProjectsProjectIdDisciplinesDisciplineId200Schema = DisciplineSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdDisciplinesDisciplineId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type GetApiProjectsProjectIdDisciplinesDisciplineId403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdDisciplinesDisciplineId404Schema = unknown;

export type GetApiProjectsProjectIdDisciplinesDisciplineIdQueryResponseSchema =
  GetApiProjectsProjectIdDisciplinesDisciplineId200Schema;

export type GetApiProjectsProjectIdDisciplinesDisciplineIdSchemaQuery = {
  Response: GetApiProjectsProjectIdDisciplinesDisciplineId200Schema;
  PathParams: GetApiProjectsProjectIdDisciplinesDisciplineIdPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdDisciplinesDisciplineId401Schema
    | GetApiProjectsProjectIdDisciplinesDisciplineId403Schema
    | GetApiProjectsProjectIdDisciplinesDisciplineId404Schema;
};
