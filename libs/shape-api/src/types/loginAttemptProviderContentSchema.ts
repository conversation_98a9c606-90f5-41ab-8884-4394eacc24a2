/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { UserSchema } from './userSchema';

export type LoginAttemptProviderContentSchema = {
  /**
   * @type boolean
   */
  accountPresent: boolean;
  /**
   * @type string
   */
  token: string;
  /**
   * @type object
   */
  tokenData: {
    /**
     * @type string
     */
    email: string;
    /**
     * @type string
     */
    firstName: string;
    /**
     * @type string
     */
    lastName: string | null;
  };
  user: UserSchema | null;
};
