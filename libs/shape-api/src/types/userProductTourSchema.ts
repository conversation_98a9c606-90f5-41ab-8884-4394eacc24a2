/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export type UserProductTourSchema = {
  /**
   * @type string
   */
  name: string;
  /**
   * @type string
   */
  key: string;
  /**
   * @type string
   */
  storylaneUrl: string;
  /**
   * @type string, date-time
   */
  firstStartedAt: string | null;
  /**
   * @type string, date-time
   */
  firstFinishedAt: string | null;
  /**
   * @type string, date-time
   */
  firstDismissedAt: string | null;
};
