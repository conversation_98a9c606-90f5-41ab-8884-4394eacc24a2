/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export type IssueEventParametersRemoveApproverSchema = {
  /**
   * @type string
   */
  eventType: string;
  /**
   * @type object
   */
  parameters: {
    /**
     * @type array
     */
    users: {
      /**
       * @type string | undefined, uuid
       */
      id?: string;
      /**
       * @type string
       */
      name: string;
      /**
       * @type integer | undefined
       */
      teamMemberId?: number;
    }[];
  };
};
