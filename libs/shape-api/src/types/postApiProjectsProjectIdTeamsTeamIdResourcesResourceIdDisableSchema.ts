/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ResourceSchema } from './resourceSchema';

export type PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisablePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
  /**
   * @type string, uuid
   */
  resource_id: string;
};

/**
 * @description Resource disabled
 */
export type PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisable200Schema = ResourceSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisable401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisable403Schema = unknown;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisable404Schema = unknown;

export type PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisableMutationResponseSchema =
  PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisable200Schema;

export type PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisableSchemaMutation = {
  Response: PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisable200Schema;
  PathParams: PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisablePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisable401Schema
    | PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisable403Schema
    | PostApiProjectsProjectIdTeamsTeamIdResourcesResourceIdDisable404Schema;
};
