/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { QueuedTaskResultFileDownloadSchema } from './queuedTaskResultFileDownloadSchema';
import type { QueuedTaskResultShowcaseProjectIdSchema } from './queuedTaskResultShowcaseProjectIdSchema';
import type { QueuedTaskResultSmartIssueSchema } from './queuedTaskResultSmartIssueSchema';

export type QueuedTaskResultSchema =
  | QueuedTaskResultFileDownloadSchema
  | QueuedTaskResultShowcaseProjectIdSchema
  | QueuedTaskResultSmartIssueSchema;
