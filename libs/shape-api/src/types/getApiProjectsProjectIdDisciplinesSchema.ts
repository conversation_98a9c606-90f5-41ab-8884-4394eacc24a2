/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DisciplineListSchema } from './disciplineListSchema';
import type { ErrorSchema } from './errorSchema';

export type GetApiProjectsProjectIdDisciplinesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export const getApiProjectsProjectIdDisciplinesQueryParamsIncludeParentsEnum = {
  true: true,
} as const;

export type GetApiProjectsProjectIdDisciplinesQueryParamsIncludeParentsEnumSchema =
  (typeof getApiProjectsProjectIdDisciplinesQueryParamsIncludeParentsEnum)[keyof typeof getApiProjectsProjectIdDisciplinesQueryParamsIncludeParentsEnum];

export type GetApiProjectsProjectIdDisciplinesQueryParamsSchema = {
  /**
   * @type string | undefined
   */
  search?: string;
  /**
   * @description If true, include parents of filtered disciplines
   * @type boolean | undefined
   */
  include_parents?: GetApiProjectsProjectIdDisciplinesQueryParamsIncludeParentsEnumSchema;
};

/**
 * @description List of disciplines
 */
export type GetApiProjectsProjectIdDisciplines200Schema = DisciplineListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdDisciplines401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdDisciplines403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdDisciplines404Schema = unknown;

export type GetApiProjectsProjectIdDisciplinesQueryResponseSchema = GetApiProjectsProjectIdDisciplines200Schema;

export type GetApiProjectsProjectIdDisciplinesSchemaQuery = {
  Response: GetApiProjectsProjectIdDisciplines200Schema;
  PathParams: GetApiProjectsProjectIdDisciplinesPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdDisciplinesQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdDisciplines401Schema
    | GetApiProjectsProjectIdDisciplines403Schema
    | GetApiProjectsProjectIdDisciplines404Schema;
};
