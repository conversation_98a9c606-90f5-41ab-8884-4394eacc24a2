/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const potentialChangeEstimatedCostImpactEnum = {
  none: 'none',
  minor: 'minor',
  moderate: 'moderate',
  significant: 'significant',
  major: 'major',
  extensive: 'extensive',
} as const;

export type PotentialChangeEstimatedCostImpactEnumSchema =
  (typeof potentialChangeEstimatedCostImpactEnum)[keyof typeof potentialChangeEstimatedCostImpactEnum];

export type PotentialChangeEstimatedCostImpactSchema = PotentialChangeEstimatedCostImpactEnumSchema;
