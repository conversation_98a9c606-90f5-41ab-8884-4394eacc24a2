/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueEventUpdateDateTimeChangeParametersSchema } from './issueEventUpdateDateTimeChangeParametersSchema';
import type { IssueEventUpdateStringChangeParametersSchema } from './issueEventUpdateStringChangeParametersSchema';
import type { IssueImpactSchema } from './issueImpactSchema';

export type IssueEventParametersUpdateImpactSchema = {
  /**
   * @type string
   */
  eventType: string;
  /**
   * @type object
   */
  parameters: {
    /**
     * @type string | undefined, date-time
     */
    delayStart?: string;
    /**
     * @type string | undefined, date-time
     */
    delayFinish?: string;
    /**
     * @type string | undefined, date-time
     */
    dueDate?: string;
    /**
     * @type string | undefined
     */
    impact?: IssueImpactSchema;
    /**
     * @type string | undefined
     */
    workAffected?: string;
    /**
     * @type object | undefined
     */
    changes?: {
      /**
       * @type object | undefined
       */
      delayStart?: IssueEventUpdateDateTimeChangeParametersSchema;
      /**
       * @type object | undefined
       */
      delayFinish?: IssueEventUpdateDateTimeChangeParametersSchema;
      /**
       * @type object | undefined
       */
      dueDate?: IssueEventUpdateDateTimeChangeParametersSchema;
      /**
       * @type object | undefined
       */
      impact?: {
        /**
         * @type string | undefined
         */
        to?: IssueImpactSchema;
        /**
         * @type string | undefined
         */
        from?: IssueImpactSchema;
      };
      /**
       * @type object | undefined
       */
      workAffected?: IssueEventUpdateStringChangeParametersSchema;
    };
  };
};
