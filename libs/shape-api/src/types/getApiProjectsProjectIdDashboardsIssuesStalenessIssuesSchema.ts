/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueListSchema } from './issueListSchema';

export type GetApiProjectsProjectIdDashboardsIssuesStalenessIssuesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export const getApiProjectsProjectIdDashboardsIssuesStalenessIssuesQueryParamsActivityLevelEnum = {
  active: 'active',
  idle: 'idle',
  stale: 'stale',
} as const;

export type GetApiProjectsProjectIdDashboardsIssuesStalenessIssuesQueryParamsActivityLevelEnumSchema =
  (typeof getApiProjectsProjectIdDashboardsIssuesStalenessIssuesQueryParamsActivityLevelEnum)[keyof typeof getApiProjectsProjectIdDashboardsIssuesStalenessIssuesQueryParamsActivityLevelEnum];

export type GetApiProjectsProjectIdDashboardsIssuesStalenessIssuesQueryParamsSchema = {
  /**
   * @type string | undefined
   */
  activity_level?: GetApiProjectsProjectIdDashboardsIssuesStalenessIssuesQueryParamsActivityLevelEnumSchema;
  /**
   * @description Page number
   * @type integer | undefined
   */
  page?: number;
};

/**
 * @description List
 */
export type GetApiProjectsProjectIdDashboardsIssuesStalenessIssues200Schema = IssueListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdDashboardsIssuesStalenessIssues400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdDashboardsIssuesStalenessIssues401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdDashboardsIssuesStalenessIssues403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdDashboardsIssuesStalenessIssues404Schema = unknown;

export type GetApiProjectsProjectIdDashboardsIssuesStalenessIssuesQueryResponseSchema =
  GetApiProjectsProjectIdDashboardsIssuesStalenessIssues200Schema;

export type GetApiProjectsProjectIdDashboardsIssuesStalenessIssuesSchemaQuery = {
  Response: GetApiProjectsProjectIdDashboardsIssuesStalenessIssues200Schema;
  PathParams: GetApiProjectsProjectIdDashboardsIssuesStalenessIssuesPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdDashboardsIssuesStalenessIssuesQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdDashboardsIssuesStalenessIssues400Schema
    | GetApiProjectsProjectIdDashboardsIssuesStalenessIssues401Schema
    | GetApiProjectsProjectIdDashboardsIssuesStalenessIssues403Schema
    | GetApiProjectsProjectIdDashboardsIssuesStalenessIssues404Schema;
};
