/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export const issueAssignmentStatusEnum = {
  pending: 'pending',
  accepted: 'accepted',
  rejected: 'rejected',
  cancelled: 'cancelled',
} as const;

export type IssueAssignmentStatusEnumSchema =
  (typeof issueAssignmentStatusEnum)[keyof typeof issueAssignmentStatusEnum];

export type IssueAssignmentStatusSchema = IssueAssignmentStatusEnumSchema;
