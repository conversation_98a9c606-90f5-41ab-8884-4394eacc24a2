/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { TimeZoneListSchema } from './timeZoneListSchema';

/**
 * @description List of time zones
 */
export type GetApiTimeZones200Schema = TimeZoneListSchema;

/**
 * @description Authentication required
 */
export type GetApiTimeZones401Schema = AuthenticationErrorSchema;

export type GetApiTimeZonesQueryResponseSchema = GetApiTimeZones200Schema;

export type GetApiTimeZonesSchemaQuery = {
  Response: GetApiTimeZones200Schema;
  Errors: GetApiTimeZones401Schema;
};
