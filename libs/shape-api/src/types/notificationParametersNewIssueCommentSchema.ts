/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersNewIssueCommentTypeEnum = {
  new_issue_comment: 'new_issue_comment',
} as const;

export type NotificationParametersNewIssueCommentTypeEnumSchema =
  (typeof notificationParametersNewIssueCommentTypeEnum)[keyof typeof notificationParametersNewIssueCommentTypeEnum];

export type NotificationParametersNewIssueCommentSchema = {
  /**
   * @type string
   */
  type: NotificationParametersNewIssueCommentTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    commentId: string;
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type string
     */
    message: string;
    /**
     * @type string, uuid
     */
    projectId: string;
  };
};
