/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';

export type PatchApiProjectsProjectIdGroupsGroupIdMembersPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  group_id: string;
};

/**
 * @description Group members updated
 */
export type PatchApiProjectsProjectIdGroupsGroupIdMembers204Schema = unknown;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdGroupsGroupIdMembers401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdGroupsGroupIdMembers403Schema = ErrorSchema;

/**
 * @description Group not found
 */
export type PatchApiProjectsProjectIdGroupsGroupIdMembers404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdGroupsGroupIdMembers422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdGroupsGroupIdMembersMutationRequestSchema = {
  /**
   * @type array | undefined
   */
  add_user_ids?: string[];
  /**
   * @type array | undefined
   */
  remove_user_ids?: string[];
  /**
   * @type array | undefined
   */
  add_admin_user_ids?: string[];
  /**
   * @type array | undefined
   */
  remove_admin_user_ids?: string[];
};

export type PatchApiProjectsProjectIdGroupsGroupIdMembersMutationResponseSchema =
  PatchApiProjectsProjectIdGroupsGroupIdMembers204Schema;

export type PatchApiProjectsProjectIdGroupsGroupIdMembersSchemaMutation = {
  Response: PatchApiProjectsProjectIdGroupsGroupIdMembers204Schema;
  Request: PatchApiProjectsProjectIdGroupsGroupIdMembersMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdGroupsGroupIdMembersPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdGroupsGroupIdMembers401Schema
    | PatchApiProjectsProjectIdGroupsGroupIdMembers403Schema
    | PatchApiProjectsProjectIdGroupsGroupIdMembers404Schema
    | PatchApiProjectsProjectIdGroupsGroupIdMembers422Schema;
};
