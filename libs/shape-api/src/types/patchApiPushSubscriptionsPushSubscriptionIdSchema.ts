/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { PushSubscriptionSchema } from './pushSubscriptionSchema';

export type PatchApiPushSubscriptionsPushSubscriptionIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  push_subscription_id: string;
};

/**
 * @description Push subscription updated
 */
export type PatchApiPushSubscriptionsPushSubscriptionId200Schema = PushSubscriptionSchema;

/**
 * @description Authentication required
 */
export type PatchApiPushSubscriptionsPushSubscriptionId401Schema = AuthenticationErrorSchema;

/**
 * @description Push subscription not found
 */
export type PatchApiPushSubscriptionsPushSubscriptionId404Schema = unknown;

export type PatchApiPushSubscriptionsPushSubscriptionIdMutationRequestSchema = {
  /**
   * @type string
   */
  endpoint: string;
  /**
   * @type object
   */
  keys: {
    /**
     * @type string
     */
    p256dh: string;
    /**
     * @type string
     */
    auth: string;
  };
};

export type PatchApiPushSubscriptionsPushSubscriptionIdMutationResponseSchema =
  PatchApiPushSubscriptionsPushSubscriptionId200Schema;

export type PatchApiPushSubscriptionsPushSubscriptionIdSchemaMutation = {
  Response: PatchApiPushSubscriptionsPushSubscriptionId200Schema;
  Request: PatchApiPushSubscriptionsPushSubscriptionIdMutationRequestSchema;
  PathParams: PatchApiPushSubscriptionsPushSubscriptionIdPathParamsSchema;
  Errors: PatchApiPushSubscriptionsPushSubscriptionId401Schema | PatchApiPushSubscriptionsPushSubscriptionId404Schema;
};
