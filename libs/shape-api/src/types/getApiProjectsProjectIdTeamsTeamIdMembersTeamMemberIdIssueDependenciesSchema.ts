/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamMemberIssueDependencyListSchema } from './teamMemberIssueDependencyListSchema';

export type GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependenciesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
  /**
   * @type integer
   */
  team_member_id: number;
};

/**
 * @description Issue dependencies
 */
export type GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependencies200Schema =
  TeamMemberIssueDependencyListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependencies401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependencies403Schema = ErrorSchema;

/**
 * @description Team member not found
 */
export type GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependencies404Schema = unknown;

export type GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependenciesQueryResponseSchema =
  GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependencies200Schema;

export type GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependenciesSchemaQuery = {
  Response: GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependencies200Schema;
  PathParams: GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependenciesPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependencies401Schema
    | GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependencies403Schema
    | GetApiProjectsProjectIdTeamsTeamIdMembersTeamMemberIdIssueDependencies404Schema;
};
