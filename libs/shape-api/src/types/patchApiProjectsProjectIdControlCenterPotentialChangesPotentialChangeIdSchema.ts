/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { PotentialChangeCategorySchema } from './potentialChangeCategorySchema';
import type { PotentialChangeEstimatedCostImpactSchema } from './potentialChangeEstimatedCostImpactSchema';
import type { PotentialChangeEstimatedScheduleImpactSchema } from './potentialChangeEstimatedScheduleImpactSchema';
import type { PotentialChangePrioritySchema } from './potentialChangePrioritySchema';
import type { PotentialChangeSchema } from './potentialChangeSchema';
import type { PotentialChangeStatusSchema } from './potentialChangeStatusSchema';

export type PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  potential_change_id: string;
};

/**
 * @description Potential change updated
 */
export type PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId200Schema = PotentialChangeSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId403Schema = ErrorSchema;

/**
 * @description Potential change not found
 */
export type PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId404Schema = unknown;

export type PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  category?: PotentialChangeCategorySchema;
  /**
   * @type string | undefined
   */
  comment?: string;
  /**
   * @type boolean | undefined
   */
  early_warning_notice_submitted?: boolean;
  /**
   * @type string | undefined
   */
  estimated_cost_impact?: PotentialChangeEstimatedCostImpactSchema;
  /**
   * @type string | undefined
   */
  estimated_schedule_impact?: PotentialChangeEstimatedScheduleImpactSchema;
  /**
   * @type string | undefined
   */
  priority?: PotentialChangePrioritySchema;
  /**
   * @type string | undefined
   */
  status?: PotentialChangeStatusSchema;
  /**
   * @type string | undefined
   */
  title?: string;
};

export type PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationResponseSchema =
  PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId200Schema;

export type PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId200Schema;
  Request: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId401Schema
    | PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId403Schema
    | PatchApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId404Schema;
};
