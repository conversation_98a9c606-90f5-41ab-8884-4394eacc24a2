/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { CommentSchema } from './commentSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiProjectsProjectIdIssuesIssueIdCommentsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Comment created
 */
export type PostApiProjectsProjectIdIssuesIssueIdComments201Schema = CommentSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdComments401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type PostApiProjectsProjectIdIssuesIssueIdComments403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdComments404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdCommentsMutationRequestSchema = {
  /**
   * @description Comment body in plain text
   * @type string
   */
  comment: string;
  /**
   * @type boolean | undefined
   */
  private?: boolean;
  /**
   * @type array | undefined
   */
  mentioned_team_member_ids?: number[];
  /**
   * @description Comment body in rich text
   * @type object | undefined
   */
  rich_text?: object;
};

export type PostApiProjectsProjectIdIssuesIssueIdCommentsMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdComments201Schema;

export type PostApiProjectsProjectIdIssuesIssueIdCommentsSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdComments201Schema;
  Request: PostApiProjectsProjectIdIssuesIssueIdCommentsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdCommentsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdComments401Schema
    | PostApiProjectsProjectIdIssuesIssueIdComments403Schema
    | PostApiProjectsProjectIdIssuesIssueIdComments404Schema;
};
