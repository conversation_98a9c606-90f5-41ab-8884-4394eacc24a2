/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { ShiftActivityStatusSchema } from './shiftActivityStatusSchema';

export type ShiftActivitySchema = {
  /**
   * @type boolean | undefined
   */
  archived?: boolean;
  /**
   * @type string, date-time
   */
  actualEndDate: string | null;
  /**
   * @type string, date-time
   */
  actualStartDate: string | null;
  /**
   * @type integer
   */
  assignedTeamMemberId: number | null;
  /**
   * @type object
   */
  blockers: {
    /**
     * @type integer
     */
    blockersCount: number;
    /**
     * @type integer
     */
    resolvedBlockersCount: number;
  };
  /**
   * @type string, date-time
   */
  createdAt: string;
  /**
   * @type boolean
   */
  critical: boolean;
  /**
   * @type string
   */
  description: string;
  /**
   * @type string, date-time
   */
  expectedFinishDate: string | null;
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string, uuid
   */
  locationId: string | null;
  /**
   * @type string
   */
  organisation: string | null;
  /**
   * @type string, uuid
   */
  organisationResourceId: string | null;
  /**
   * @type integer
   */
  ownerId: number | null;
  /**
   * @type number, float
   */
  percentageCompleted: number | null;
  /**
   * @type string, date-time
   */
  plannedEndDate: string | null;
  /**
   * @type string, date-time
   */
  plannedStartDate: string | null;
  /**
   * @type object
   */
  readiness: {
    /**
     * @type integer
     */
    completedRequirements: number;
    /**
     * @type boolean
     */
    ready: boolean;
    /**
     * @type integer
     */
    totalRequirements: number;
    /**
     * @type string, date-time
     */
    updatedAt: string | null;
    /**
     * @type integer
     */
    updaterId: number | null;
  };
  /**
   * @type string
   */
  referenceNumber: string;
  status: ShiftActivityStatusSchema | null;
  /**
   * @type string
   */
  taskIdentifier: string | null;
  /**
   * @type string, uuid
   */
  teamId: string;
  /**
   * @type integer
   */
  teamMemberId: number;
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    archive: boolean;
    /**
     * @type boolean
     */
    edit: boolean;
    /**
     * @type boolean
     */
    restore: boolean;
  };
};
