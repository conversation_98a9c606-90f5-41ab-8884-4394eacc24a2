/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamChannelConfigurationSchema } from './teamChannelConfigurationSchema';

export type PatchApiProjectsProjectIdTeamsTeamIdChannelConfigurationPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
};

/**
 * @description Updated team channel configuration
 */
export type PatchApiProjectsProjectIdTeamsTeamIdChannelConfiguration200Schema = TeamChannelConfigurationSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdTeamsTeamIdChannelConfiguration401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdTeamsTeamIdChannelConfiguration403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PatchApiProjectsProjectIdTeamsTeamIdChannelConfiguration404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdTeamsTeamIdChannelConfiguration422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdTeamsTeamIdChannelConfigurationMutationRequestSchema = {
  /**
   * @type boolean | undefined
   */
  auto_upload_documents?: boolean;
};

export type PatchApiProjectsProjectIdTeamsTeamIdChannelConfigurationMutationResponseSchema =
  PatchApiProjectsProjectIdTeamsTeamIdChannelConfiguration200Schema;

export type PatchApiProjectsProjectIdTeamsTeamIdChannelConfigurationSchemaMutation = {
  Response: PatchApiProjectsProjectIdTeamsTeamIdChannelConfiguration200Schema;
  Request: PatchApiProjectsProjectIdTeamsTeamIdChannelConfigurationMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdTeamsTeamIdChannelConfigurationPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdTeamsTeamIdChannelConfiguration401Schema
    | PatchApiProjectsProjectIdTeamsTeamIdChannelConfiguration403Schema
    | PatchApiProjectsProjectIdTeamsTeamIdChannelConfiguration404Schema
    | PatchApiProjectsProjectIdTeamsTeamIdChannelConfiguration422Schema;
};
