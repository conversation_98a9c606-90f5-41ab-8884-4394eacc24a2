/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const trackedInTypeEnum = {
  shift_report: 'shift_report',
  weekly_work_plan: 'weekly_work_plan',
} as const;

export type TrackedInTypeEnumSchema = (typeof trackedInTypeEnum)[keyof typeof trackedInTypeEnum];

export type ShiftActivityProgressLogBasicDetailsSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  comment: string | null;
  /**
   * @type string
   */
  description: string | null;
  /**
   * @type string, date-time
   */
  createdAt: string;
  /**
   * @type integer
   */
  createdById: number;
  /**
   * @type string, date
   */
  date: string;
  /**
   * @type integer
   */
  documentCount: number;
  /**
   * @type string, date-time
   */
  lastUpdatedAt: string | null;
  /**
   * @type integer
   */
  lastUpdatedById: number | null;
  /**
   * @type number, float
   */
  percentageCompleted: number | null;
  /**
   * @type number, float
   */
  quantity: number | null;
  /**
   * @type string
   */
  units: string | null;
  /**
   * @type string, uuid
   */
  shiftActivityId: string;
  /**
   * @type object
   */
  trackedIn: {
    /**
     * @type string, uuid
     */
    id: string;
    /**
     * @type string
     */
    type: TrackedInTypeEnumSchema;
  } | null;
};
