/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export const issueImpactEnum = {
  completedDelay: 'completedDelay',
  liveDelay: 'liveDelay',
  noEffect: 'noEffect',
  potentialDelay: 'potentialDelay',
} as const;

export type IssueImpactEnumSchema = (typeof issueImpactEnum)[keyof typeof issueImpactEnum];

export type IssueImpactSchema = IssueImpactEnumSchema;
