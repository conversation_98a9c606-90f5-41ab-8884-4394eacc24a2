/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ShiftReportResetSectionErrorSchema } from './shiftReportResetSectionErrorSchema';
import type { ShiftReportSchema } from './shiftReportSchema';

export type PostApiProjectsProjectIdShiftReportsShiftReportIdResetSectionPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

export const postApiProjectsProjectIdShiftReportsShiftReportIdResetSectionQueryParamsSectionEnum = {
  activities: 'activities',
  contract_forces: 'contract_forces',
  down_times: 'down_times',
  equipments: 'equipments',
  materials: 'materials',
  safety_health_environments: 'safety_health_environments',
} as const;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdResetSectionQueryParamsSectionEnumSchema =
  (typeof postApiProjectsProjectIdShiftReportsShiftReportIdResetSectionQueryParamsSectionEnum)[keyof typeof postApiProjectsProjectIdShiftReportsShiftReportIdResetSectionQueryParamsSectionEnum];

export type PostApiProjectsProjectIdShiftReportsShiftReportIdResetSectionQueryParamsSchema = {
  /**
   * @type string | undefined
   */
  section?: PostApiProjectsProjectIdShiftReportsShiftReportIdResetSectionQueryParamsSectionEnumSchema;
};

/**
 * @description Shift report section reset
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdResetSection200Schema = ShiftReportSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdResetSection400Schema = ShiftReportResetSectionErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdResetSection401Schema = AuthenticationErrorSchema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdResetSectionMutationResponseSchema =
  PostApiProjectsProjectIdShiftReportsShiftReportIdResetSection200Schema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdResetSectionSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftReportsShiftReportIdResetSection200Schema;
  PathParams: PostApiProjectsProjectIdShiftReportsShiftReportIdResetSectionPathParamsSchema;
  QueryParams: PostApiProjectsProjectIdShiftReportsShiftReportIdResetSectionQueryParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdResetSection400Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdResetSection401Schema;
};
