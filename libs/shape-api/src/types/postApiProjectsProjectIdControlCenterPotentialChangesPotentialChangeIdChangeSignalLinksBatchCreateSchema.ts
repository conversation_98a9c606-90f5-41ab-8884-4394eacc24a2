/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ChangeSignalsBodyParameterSchema } from './changeSignalsBodyParameterSchema';
import type { ErrorSchema } from './errorSchema';
import type { PotentialChangeSchema } from './potentialChangeSchema';

export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreatePathParamsSchema =
  {
    /**
     * @type string, uuid
     */
    project_id: string;
    /**
     * @type string, uuid
     */
    potential_change_id: string;
  };

/**
 * @description Creates potential change change signals links
 */
export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreate200Schema =
  PotentialChangeSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreate401Schema =
  AuthenticationErrorSchema;

/**
 * @description Batch create failed
 */
export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreate422Schema =
  ErrorSchema;

export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMutationRequestSchema =
  ChangeSignalsBodyParameterSchema;

export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMutationResponseSchema =
  PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreate200Schema;

export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateSchemaMutation =
  {
    Response: PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreate200Schema;
    Request: PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreateMutationRequestSchema;
    PathParams: PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreatePathParamsSchema;
    Errors:
      | PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreate401Schema
      | PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdChangeSignalLinksBatchCreate422Schema;
  };
