/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DisciplineSchema } from './disciplineSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiProjectsProjectIdDisciplinesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Discipline created
 */
export type PostApiProjectsProjectIdDisciplines201Schema = DisciplineSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdDisciplines401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdDisciplines403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdDisciplines404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdDisciplines422Schema = ErrorSchema;

export type PostApiProjectsProjectIdDisciplinesMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  name?: string;
  /**
   * @type string | undefined
   */
  short_code?: string;
  /**
   * @type string | undefined, uuid
   */
  parent_discipline_id?: string;
  /**
   * @type integer | undefined
   */
  sort_position?: number;
};

export type PostApiProjectsProjectIdDisciplinesMutationResponseSchema = PostApiProjectsProjectIdDisciplines201Schema;

export type PostApiProjectsProjectIdDisciplinesSchemaMutation = {
  Response: PostApiProjectsProjectIdDisciplines201Schema;
  Request: PostApiProjectsProjectIdDisciplinesMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdDisciplinesPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdDisciplines401Schema
    | PostApiProjectsProjectIdDisciplines403Schema
    | PostApiProjectsProjectIdDisciplines404Schema
    | PostApiProjectsProjectIdDisciplines422Schema;
};
