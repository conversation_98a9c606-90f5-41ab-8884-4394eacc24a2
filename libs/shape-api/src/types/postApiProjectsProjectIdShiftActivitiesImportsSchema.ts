/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiProjectsProjectIdShiftActivitiesImportsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Shift activity import created
 */
export type PostApiProjectsProjectIdShiftActivitiesImports202Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftActivitiesImports401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftActivitiesImports403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftActivitiesImports404Schema = unknown;

/**
 * @description Create failed
 */
export type PostApiProjectsProjectIdShiftActivitiesImports422Schema = ErrorSchema;

export type PostApiProjectsProjectIdShiftActivitiesImportsMutationRequestSchema = {
  /**
   * @description The XLSX file
   * @type string | undefined, binary
   */
  xlsx?: Blob;
};

export type PostApiProjectsProjectIdShiftActivitiesImportsMutationResponseSchema =
  PostApiProjectsProjectIdShiftActivitiesImports202Schema;

export type PostApiProjectsProjectIdShiftActivitiesImportsSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftActivitiesImports202Schema;
  Request: PostApiProjectsProjectIdShiftActivitiesImportsMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftActivitiesImportsPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftActivitiesImports401Schema
    | PostApiProjectsProjectIdShiftActivitiesImports403Schema
    | PostApiProjectsProjectIdShiftActivitiesImports404Schema
    | PostApiProjectsProjectIdShiftActivitiesImports422Schema;
};
