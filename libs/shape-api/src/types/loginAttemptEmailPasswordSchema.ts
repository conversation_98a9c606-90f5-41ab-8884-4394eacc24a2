/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

export type LoginAttemptEmailPasswordSchema = {
  /**
   * @type string
   */
  strategy: string;
  /**
   * @type object
   */
  emailPassword: {
    /**
     * @type boolean
     */
    accountPresent: boolean;
    /**
     * @type string
     */
    email: string;
    /**
     * @type boolean
     */
    ssoRequired: boolean;
  };
};
