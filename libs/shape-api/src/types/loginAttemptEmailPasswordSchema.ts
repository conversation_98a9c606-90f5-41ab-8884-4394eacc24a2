/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const loginAttemptEmailPasswordStrategyEnum = {
  email_password: 'email_password',
} as const;

export type LoginAttemptEmailPasswordStrategyEnumSchema =
  (typeof loginAttemptEmailPasswordStrategyEnum)[keyof typeof loginAttemptEmailPasswordStrategyEnum];

export type LoginAttemptEmailPasswordSchema = {
  /**
   * @type string
   */
  strategy: LoginAttemptEmailPasswordStrategyEnumSchema;
  /**
   * @type object
   */
  emailPassword: {
    /**
     * @type boolean
     */
    accountPresent: boolean;
    /**
     * @type string
     */
    email: string;
    /**
     * @type boolean
     */
    ssoRequired: boolean;
  };
};
