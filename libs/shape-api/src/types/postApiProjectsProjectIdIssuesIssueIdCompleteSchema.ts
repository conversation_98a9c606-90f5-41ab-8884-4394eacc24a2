/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueSchema } from './issueSchema';

export type PostApiProjectsProjectIdIssuesIssueIdCompletePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue updated
 */
export type PostApiProjectsProjectIdIssuesIssueIdComplete200Schema = IssueSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdIssuesIssueIdComplete400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdComplete401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesIssueIdComplete403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdComplete404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdCompleteMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdComplete200Schema;

export type PostApiProjectsProjectIdIssuesIssueIdCompleteSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdComplete200Schema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdCompletePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdComplete400Schema
    | PostApiProjectsProjectIdIssuesIssueIdComplete401Schema
    | PostApiProjectsProjectIdIssuesIssueIdComplete403Schema
    | PostApiProjectsProjectIdIssuesIssueIdComplete404Schema;
};
