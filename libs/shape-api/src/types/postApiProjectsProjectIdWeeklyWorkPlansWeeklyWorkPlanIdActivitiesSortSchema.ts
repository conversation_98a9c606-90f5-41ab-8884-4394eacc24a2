/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSortPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

/**
 * @description Weekly work plan activities sorted
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSort204Schema = unknown;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSort400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSort401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSort403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSort404Schema = unknown;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSortMutationRequestSchema = {
  /**
   * @type array
   */
  ordered_weekly_work_plan_activity_ids: string[];
};

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSortMutationResponseSchema =
  PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSort204Schema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSortSchemaMutation = {
  Response: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSort204Schema;
  Request: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSortMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSortPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSort400Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSort401Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSort403Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdActivitiesSort404Schema;
};
