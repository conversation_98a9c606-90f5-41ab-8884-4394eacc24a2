/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ProjectAccessRequestSchema } from './projectAccessRequestSchema';

export type GetApiProjectsProjectIdAccessRequestsProjectAccessRequestIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  project_access_request_id: string;
};

/**
 * @description Project access request found
 */
export type GetApiProjectsProjectIdAccessRequestsProjectAccessRequestId200Schema = ProjectAccessRequestSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdAccessRequestsProjectAccessRequestId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdAccessRequestsProjectAccessRequestId403Schema = ErrorSchema;

/**
 * @description Request not found
 */
export type GetApiProjectsProjectIdAccessRequestsProjectAccessRequestId404Schema = unknown;

export type GetApiProjectsProjectIdAccessRequestsProjectAccessRequestIdQueryResponseSchema =
  GetApiProjectsProjectIdAccessRequestsProjectAccessRequestId200Schema;

export type GetApiProjectsProjectIdAccessRequestsProjectAccessRequestIdSchemaQuery = {
  Response: GetApiProjectsProjectIdAccessRequestsProjectAccessRequestId200Schema;
  PathParams: GetApiProjectsProjectIdAccessRequestsProjectAccessRequestIdPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdAccessRequestsProjectAccessRequestId401Schema
    | GetApiProjectsProjectIdAccessRequestsProjectAccessRequestId403Schema
    | GetApiProjectsProjectIdAccessRequestsProjectAccessRequestId404Schema;
};
