/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { ShiftReportQualityIndicatorsQuantityGroupSchema } from './shiftReportQualityIndicatorsQuantityGroupSchema';

export type ShiftReportQualityIndicatorsSchema = {
  /**
   * @type object
   */
  currentScore: {
    /**
     * @type object
     */
    percentage: ShiftReportQualityIndicatorsQuantityGroupSchema;
  };
  /**
   * @type object
   */
  basics: {
    /**
     * @type object
     */
    items: {
      /**
       * @type boolean
       */
      activityOrDowntime: boolean;
      /**
       * @type boolean
       */
      reportDate: boolean;
      /**
       * @type boolean
       */
      reportTitle: boolean;
      /**
       * @type boolean
       */
      weatherDescription: boolean;
    };
    /**
     * @type object
     */
    percentage: ShiftReportQualityIndicatorsQuantityGroupSchema;
  };
  /**
   * @type object
   */
  people: {
    /**
     * @type object
     */
    percentage: ShiftReportQualityIndicatorsQuantityGroupSchema;
  };
  /**
   * @type object
   */
  equipment: {
    /**
     * @type object
     */
    percentage: ShiftReportQualityIndicatorsQuantityGroupSchema;
  };
  /**
   * @type object
   */
  material: {
    /**
     * @type object
     */
    percentage: ShiftReportQualityIndicatorsQuantityGroupSchema;
  };
  /**
   * @type object
   */
  evidence: {
    /**
     * @type object
     */
    items: ShiftReportQualityIndicatorsQuantityGroupSchema;
    /**
     * @type object
     */
    percentage: ShiftReportQualityIndicatorsQuantityGroupSchema;
  };
  /**
   * @type object
   */
  allocations: {
    /**
     * @type object
     */
    items: ShiftReportQualityIndicatorsQuantityGroupSchema;
    /**
     * @type object
     */
    percentage: ShiftReportQualityIndicatorsQuantityGroupSchema;
  };
};
