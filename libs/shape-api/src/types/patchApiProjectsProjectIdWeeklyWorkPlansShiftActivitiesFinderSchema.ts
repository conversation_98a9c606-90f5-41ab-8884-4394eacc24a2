/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { WeeklyWorkPlanShiftActivitiesFinderSchema } from './weeklyWorkPlanShiftActivitiesFinderSchema';

export type PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Updated shift activities finder options
 */
export type PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder200Schema =
  WeeklyWorkPlanShiftActivitiesFinderSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder403Schema = unknown;

/**
 * @description Project not found
 */
export type PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderMutationRequestSchema =
  WeeklyWorkPlanShiftActivitiesFinderSchema;

export type PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderMutationResponseSchema =
  PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder200Schema;

export type PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderSchemaMutation = {
  Response: PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder200Schema;
  Request: PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder401Schema
    | PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder403Schema
    | PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder404Schema
    | PatchApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder422Schema;
};
