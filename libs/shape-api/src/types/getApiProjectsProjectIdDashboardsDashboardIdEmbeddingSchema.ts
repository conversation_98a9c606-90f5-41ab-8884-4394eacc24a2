/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { DashboardEmbeddingSchema } from './dashboardEmbeddingSchema';
import type { ErrorSchema } from './errorSchema';

export type GetApiProjectsProjectIdDashboardsDashboardIdEmbeddingPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  dashboard_id: string;
};

/**
 * @description Dashboard embedding
 */
export type GetApiProjectsProjectIdDashboardsDashboardIdEmbedding200Schema = DashboardEmbeddingSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdDashboardsDashboardIdEmbedding401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdDashboardsDashboardIdEmbedding403Schema = ErrorSchema;

/**
 * @description Dashboard not found
 */
export type GetApiProjectsProjectIdDashboardsDashboardIdEmbedding404Schema = unknown;

/**
 * @description Metabase not configured
 */
export type GetApiProjectsProjectIdDashboardsDashboardIdEmbedding503Schema = unknown;

export type GetApiProjectsProjectIdDashboardsDashboardIdEmbeddingQueryResponseSchema =
  GetApiProjectsProjectIdDashboardsDashboardIdEmbedding200Schema;

export type GetApiProjectsProjectIdDashboardsDashboardIdEmbeddingSchemaQuery = {
  Response: GetApiProjectsProjectIdDashboardsDashboardIdEmbedding200Schema;
  PathParams: GetApiProjectsProjectIdDashboardsDashboardIdEmbeddingPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdDashboardsDashboardIdEmbedding401Schema
    | GetApiProjectsProjectIdDashboardsDashboardIdEmbedding403Schema
    | GetApiProjectsProjectIdDashboardsDashboardIdEmbedding404Schema
    | GetApiProjectsProjectIdDashboardsDashboardIdEmbedding503Schema;
};
