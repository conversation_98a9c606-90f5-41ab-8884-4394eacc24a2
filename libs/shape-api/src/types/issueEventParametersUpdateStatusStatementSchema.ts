/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const issueEventParametersUpdateStatusStatementEventTypeEnum = {
  update_status_statement: 'update_status_statement',
} as const;

export type IssueEventParametersUpdateStatusStatementEventTypeEnumSchema =
  (typeof issueEventParametersUpdateStatusStatementEventTypeEnum)[keyof typeof issueEventParametersUpdateStatusStatementEventTypeEnum];

export type IssueEventParametersUpdateStatusStatementSchema = {
  /**
   * @type string
   */
  eventType: IssueEventParametersUpdateStatusStatementEventTypeEnumSchema;
  /**
   * @type object
   */
  parameters: {
    /**
     * @type string, date
     */
    date: string;
    /**
     * @type string
     */
    statement: string;
  };
};
