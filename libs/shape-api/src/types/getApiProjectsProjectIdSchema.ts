/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ProjectSchema } from './projectSchema';

export type GetApiProjectsProjectIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Project found
 */
export type GetApiProjectsProjectId200Schema = ProjectSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectId403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type GetApiProjectsProjectId404Schema = unknown;

export type GetApiProjectsProjectIdQueryResponseSchema = GetApiProjectsProjectId200Schema;

export type GetApiProjectsProjectIdSchemaQuery = {
  Response: GetApiProjectsProjectId200Schema;
  PathParams: GetApiProjectsProjectIdPathParamsSchema;
  Errors: GetApiProjectsProjectId401Schema | GetApiProjectsProjectId403Schema | GetApiProjectsProjectId404Schema;
};
