/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftActivityProgressLogSchema } from './shiftActivityProgressLogSchema';

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
  /**
   * @type string, uuid
   */
  progress_log_id: string;
};

/**
 * @description Shift Activity Progress Log updated
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId200Schema =
  ShiftActivityProgressLogSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId422Schema = unknown;

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  comment?: string;
  /**
   * @type string | undefined
   */
  description?: string;
  /**
   * @type number | undefined, float
   */
  percentage_completed?: number;
  /**
   * @type number | undefined, float
   */
  quantity?: number;
  /**
   * @type string | undefined
   */
  units?: string;
};

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMutationResponseSchema =
  PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId200Schema;

export type PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId200Schema;
  Request: PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId401Schema
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId403Schema
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId404Schema
    | PatchApiProjectsProjectIdShiftActivitiesShiftActivityIdProgressLogsProgressLogId422Schema;
};
