/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueVisitSchema } from './issueVisitSchema';

export type PostApiProjectsProjectIdIssuesIssueIdVisitPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Visit saved
 */
export type PostApiProjectsProjectIdIssuesIssueIdVisit200Schema = IssueVisitSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdVisit401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesIssueIdVisit403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdVisit404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdVisitMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdVisit200Schema;

export type PostApiProjectsProjectIdIssuesIssueIdVisitSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdVisit200Schema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdVisitPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdVisit401Schema
    | PostApiProjectsProjectIdIssuesIssueIdVisit403Schema
    | PostApiProjectsProjectIdIssuesIssueIdVisit404Schema;
};
