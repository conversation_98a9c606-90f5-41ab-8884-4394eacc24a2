/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { LoginAttemptEmailPasswordSchema } from './loginAttemptEmailPasswordSchema';
import type { LoginAttemptGoogleSchema } from './loginAttemptGoogleSchema';
import type { LoginAttemptMicrosoftSchema } from './loginAttemptMicrosoftSchema';

export type LoginAttemptSchema =
  | LoginAttemptEmailPasswordSchema
  | LoginAttemptGoogleSchema
  | LoginAttemptMicrosoftSchema;
