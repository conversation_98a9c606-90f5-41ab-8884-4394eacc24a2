/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { LoginAttemptEmailPasswordSchema } from './loginAttemptEmailPasswordSchema';
import type { LoginAttemptGoogleSchema } from './loginAttemptGoogleSchema';
import type { LoginAttemptMicrosoftSchema } from './loginAttemptMicrosoftSchema';

export type LoginAttemptSchema =
  | (LoginAttemptEmailPasswordSchema & {
      strategy: 'email_password';
    })
  | (LoginAttemptGoogleSchema & {
      strategy: 'google';
    })
  | (LoginAttemptMicrosoftSchema & {
      strategy: 'microsoft';
    });
