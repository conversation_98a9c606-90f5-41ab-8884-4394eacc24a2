/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { UserSchema } from './userSchema';

/**
 * @description User authenticated
 */
export type PostApiLogin200Schema = UserSchema;

/**
 * @description Authentication failed
 */
export type PostApiLogin401Schema = AuthenticationErrorSchema;

/**
 * @description Failed request
 */
export type PostApiLogin422Schema = ErrorSchema;

export type PostApiLoginMutationRequestSchema = {
  /**
   * @type object
   */
  user: {
    /**
     * @type string
     */
    email: string;
    /**
     * @type string
     */
    password: string;
  };
};

export type PostApiLoginMutationResponseSchema = PostApiLogin200Schema;

export type PostApiLoginSchemaMutation = {
  Response: PostApiLogin200Schema;
  Request: PostApiLoginMutationRequestSchema;
  Errors: PostApiLogin401Schema | PostApiLogin422Schema;
};
