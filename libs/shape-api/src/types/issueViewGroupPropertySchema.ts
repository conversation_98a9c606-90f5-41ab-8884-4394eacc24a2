/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const issueViewGroupPropertyNameEnum = {
  groups_custom_field_id: 'groups_custom_field_id',
} as const;

export type IssueViewGroupPropertyNameEnumSchema =
  (typeof issueViewGroupPropertyNameEnum)[keyof typeof issueViewGroupPropertyNameEnum];

export type IssueViewGroupPropertySchema = {
  /**
   * @type string
   */
  name: IssueViewGroupPropertyNameEnumSchema;
  value: unknown;
};
