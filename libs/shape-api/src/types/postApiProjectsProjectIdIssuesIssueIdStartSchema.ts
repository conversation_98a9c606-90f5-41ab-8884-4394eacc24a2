/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueSchema } from './issueSchema';

export type PostApiProjectsProjectIdIssuesIssueIdStartPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue updated
 */
export type PostApiProjectsProjectIdIssuesIssueIdStart200Schema = IssueSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdIssuesIssueIdStart400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdStart401Schema = AuthenticationErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdStart404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdStartMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdStart200Schema;

export type PostApiProjectsProjectIdIssuesIssueIdStartSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdStart200Schema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdStartPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdStart400Schema
    | PostApiProjectsProjectIdIssuesIssueIdStart401Schema
    | PostApiProjectsProjectIdIssuesIssueIdStart404Schema;
};
