/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const paramsChannelEnum2 = {
  public: 'public',
  collaborators: 'collaborators',
} as const;

export type ParamsChannelEnum2Schema = (typeof paramsChannelEnum2)[keyof typeof paramsChannelEnum2];

export type NotificationParametersShiftReportCommentMentionSchema = {
  /**
   * @type string
   */
  type: string;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string
     */
    channel: ParamsChannelEnum2Schema;
    /**
     * @type string, uuid
     */
    commentId: string;
    /**
     * @type string, uuid
     */
    projectId: string;
    /**
     * @type string, date
     */
    shiftReportDate: string;
    /**
     * @type string, uuid
     */
    shiftReportId: string;
    /**
     * @type string
     */
    shiftReportTitle: string | null;
  };
};
