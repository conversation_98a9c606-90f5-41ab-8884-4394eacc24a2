/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { NotificationsOverviewSchema } from './notificationsOverviewSchema';

/**
 * @description Notifications overview
 */
export type GetApiNotificationsOverview200Schema = NotificationsOverviewSchema;

/**
 * @description Authentication required
 */
export type GetApiNotificationsOverview401Schema = AuthenticationErrorSchema;

export type GetApiNotificationsOverviewQueryResponseSchema = GetApiNotificationsOverview200Schema;

export type GetApiNotificationsOverviewSchemaQuery = {
  Response: GetApiNotificationsOverview200Schema;
  Errors: GetApiNotificationsOverview401Schema;
};
