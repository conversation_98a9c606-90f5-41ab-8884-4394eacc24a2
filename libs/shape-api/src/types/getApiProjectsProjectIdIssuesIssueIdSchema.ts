/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueSchema } from './issueSchema';

export type GetApiProjectsProjectIdIssuesIssueIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue found
 */
export type GetApiProjectsProjectIdIssuesIssueId200Schema = IssueSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdIssuesIssueId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdIssuesIssueId403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type GetApiProjectsProjectIdIssuesIssueId404Schema = unknown;

export type GetApiProjectsProjectIdIssuesIssueIdQueryResponseSchema = GetApiProjectsProjectIdIssuesIssueId200Schema;

export type GetApiProjectsProjectIdIssuesIssueIdSchemaQuery = {
  Response: GetApiProjectsProjectIdIssuesIssueId200Schema;
  PathParams: GetApiProjectsProjectIdIssuesIssueIdPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdIssuesIssueId401Schema
    | GetApiProjectsProjectIdIssuesIssueId403Schema
    | GetApiProjectsProjectIdIssuesIssueId404Schema;
};
