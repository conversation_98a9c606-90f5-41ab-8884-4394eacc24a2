/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { OrgSchema } from './orgSchema';

export type TeamSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  displayName: string | null;
  /**
   * @type boolean
   */
  independent: boolean;
  /**
   * @type string, uuid
   */
  projectId: string;
  org: OrgSchema | null;
  /**
   * @type object
   */
  availableActions: {
    /**
     * @type boolean
     */
    delete: boolean;
    /**
     * @type boolean
     */
    edit: boolean;
    /**
     * @type boolean
     */
    inviteUser: boolean;
    /**
     * @type boolean
     */
    manageJoinToken: boolean;
    /**
     * @type boolean
     */
    manageResource: boolean;
  };
};
