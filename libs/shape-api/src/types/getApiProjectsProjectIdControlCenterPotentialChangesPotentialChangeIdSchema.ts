/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { PotentialChangeSchema } from './potentialChangeSchema';

export type GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  potential_change_id: string;
};

/**
 * @description Potential change found
 */
export type GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId200Schema = PotentialChangeSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId403Schema = ErrorSchema;

/**
 * @description Potential change not found
 */
export type GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId404Schema = unknown;

export type GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdQueryResponseSchema =
  GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId200Schema;

export type GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdSchemaQuery = {
  Response: GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId200Schema;
  PathParams: GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId401Schema
    | GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId403Schema
    | GetApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeId404Schema;
};
