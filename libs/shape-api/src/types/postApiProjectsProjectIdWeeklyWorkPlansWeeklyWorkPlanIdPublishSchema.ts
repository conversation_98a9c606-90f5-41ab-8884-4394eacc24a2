/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { WeeklyWorkPlanSchema } from './weeklyWorkPlanSchema';

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublishPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  weekly_work_plan_id: string;
};

/**
 * @description Weekly Work Plan published
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublish200Schema = WeeklyWorkPlanSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublish401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublish403Schema = unknown;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublish404Schema = unknown;

/**
 * @description Publish failed
 */
export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublish422Schema = ErrorSchema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublishMutationResponseSchema =
  PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublish200Schema;

export type PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublishSchemaMutation = {
  Response: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublish200Schema;
  PathParams: PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublishPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublish401Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublish403Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublish404Schema
    | PostApiProjectsProjectIdWeeklyWorkPlansWeeklyWorkPlanIdPublish422Schema;
};
