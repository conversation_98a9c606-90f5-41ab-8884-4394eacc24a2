/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { PotentialChangeSchema } from './potentialChangeSchema';

export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchivePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  potential_change_id: string;
};

/**
 * @description Potential change archived
 */
export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchive200Schema =
  PotentialChangeSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchive401Schema =
  AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchive403Schema = ErrorSchema;

/**
 * @description Potential change not found
 */
export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchive404Schema = unknown;

/**
 * @description Archive failed
 */
export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchive422Schema = ErrorSchema;

export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchiveMutationResponseSchema =
  PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchive200Schema;

export type PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchiveSchemaMutation = {
  Response: PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchive200Schema;
  PathParams: PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchivePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchive401Schema
    | PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchive403Schema
    | PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchive404Schema
    | PostApiProjectsProjectIdControlCenterPotentialChangesPotentialChangeIdArchive422Schema;
};
