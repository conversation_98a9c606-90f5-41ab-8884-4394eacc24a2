/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';

export type PostApiProjectsProjectIdLocationsLocationIdSortPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  location_id: string;
};

/**
 * @description Locations sorted
 */
export type PostApiProjectsProjectIdLocationsLocationIdSort204Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdLocationsLocationIdSort401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdLocationsLocationIdSort403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type PostApiProjectsProjectIdLocationsLocationIdSort404Schema = unknown;

export type PostApiProjectsProjectIdLocationsLocationIdSortMutationRequestSchema = {
  /**
   * @type array | undefined
   */
  ordered_location_ids?: string[];
};

export type PostApiProjectsProjectIdLocationsLocationIdSortMutationResponseSchema =
  PostApiProjectsProjectIdLocationsLocationIdSort204Schema;

export type PostApiProjectsProjectIdLocationsLocationIdSortSchemaMutation = {
  Response: PostApiProjectsProjectIdLocationsLocationIdSort204Schema;
  Request: PostApiProjectsProjectIdLocationsLocationIdSortMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdLocationsLocationIdSortPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdLocationsLocationIdSort401Schema
    | PostApiProjectsProjectIdLocationsLocationIdSort403Schema
    | PostApiProjectsProjectIdLocationsLocationIdSort404Schema;
};
