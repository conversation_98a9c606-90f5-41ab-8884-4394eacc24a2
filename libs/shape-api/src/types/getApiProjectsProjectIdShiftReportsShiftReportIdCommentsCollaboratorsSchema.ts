/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftReportCommentListSchema } from './shiftReportCommentListSchema';

export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsQueryParamsSchema = {
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description Comments
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators200Schema =
  ShiftReportCommentListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators403Schema = ErrorSchema;

/**
 * @description Shift report not found
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators404Schema = unknown;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsQueryResponseSchema =
  GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators200Schema;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators200Schema;
  PathParams: GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaboratorsQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators400Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators401Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators403Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdCommentsCollaborators404Schema;
};
