/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersIssueWasResolvedTypeEnum = {
  issue_was_resolved: 'issue_was_resolved',
} as const;

export type NotificationParametersIssueWasResolvedTypeEnumSchema =
  (typeof notificationParametersIssueWasResolvedTypeEnum)[keyof typeof notificationParametersIssueWasResolvedTypeEnum];

export type NotificationParametersIssueWasResolvedSchema = {
  /**
   * @type string
   */
  type: NotificationParametersIssueWasResolvedTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type string
     */
    progress: string;
    /**
     * @type string, uuid
     */
    projectId: string;
  };
};
