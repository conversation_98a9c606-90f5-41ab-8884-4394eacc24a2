/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamListSchema } from './teamListSchema';

export type GetApiProjectsProjectIdTeamsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description List of teams
 */
export type GetApiProjectsProjectIdTeams200Schema = TeamListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdTeams401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdTeams403Schema = ErrorSchema;

/**
 * @description Project not found
 */
export type GetApiProjectsProjectIdTeams404Schema = unknown;

export type GetApiProjectsProjectIdTeamsQueryResponseSchema = GetApiProjectsProjectIdTeams200Schema;

export type GetApiProjectsProjectIdTeamsSchemaQuery = {
  Response: GetApiProjectsProjectIdTeams200Schema;
  PathParams: GetApiProjectsProjectIdTeamsPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdTeams401Schema
    | GetApiProjectsProjectIdTeams403Schema
    | GetApiProjectsProjectIdTeams404Schema;
};
