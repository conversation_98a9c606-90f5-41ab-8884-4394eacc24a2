/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamMemberListSchema } from './teamMemberListSchema';

export type GetApiProjectsProjectIdShiftReportsShiftReportIdPeoplePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

export type GetApiProjectsProjectIdShiftReportsShiftReportIdPeopleQueryParamsSchema = {
  /**
   * @type string | undefined
   */
  search?: string;
};

/**
 * @description People
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdPeople200Schema = TeamMemberListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdPeople401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdPeople403Schema = ErrorSchema;

/**
 * @description Shift report not found
 */
export type GetApiProjectsProjectIdShiftReportsShiftReportIdPeople404Schema = unknown;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdPeopleQueryResponseSchema =
  GetApiProjectsProjectIdShiftReportsShiftReportIdPeople200Schema;

export type GetApiProjectsProjectIdShiftReportsShiftReportIdPeopleSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftReportsShiftReportIdPeople200Schema;
  PathParams: GetApiProjectsProjectIdShiftReportsShiftReportIdPeoplePathParamsSchema;
  QueryParams: GetApiProjectsProjectIdShiftReportsShiftReportIdPeopleQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftReportsShiftReportIdPeople401Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdPeople403Schema
    | GetApiProjectsProjectIdShiftReportsShiftReportIdPeople404Schema;
};
