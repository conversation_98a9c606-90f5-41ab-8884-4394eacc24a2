/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueImageKindSchema } from './issueImageKindSchema';
import type { IssueImageListSchema } from './issueImageListSchema';

export type GetApiProjectsProjectIdIssuesIssueIdIssueImagesPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

export type GetApiProjectsProjectIdIssuesIssueIdIssueImagesQueryParamsSchema = {
  /**
   * @type string | undefined
   */
  kind?: IssueImageKindSchema;
};

/**
 * @description List of issue images
 * @deprecated
 */
export type GetApiProjectsProjectIdIssuesIssueIdIssueImages200Schema = IssueImageListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdIssuesIssueIdIssueImages401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdIssuesIssueIdIssueImages403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdIssuesIssueIdIssueImages404Schema = unknown;

export type GetApiProjectsProjectIdIssuesIssueIdIssueImagesQueryResponseSchema =
  GetApiProjectsProjectIdIssuesIssueIdIssueImages200Schema;

export type GetApiProjectsProjectIdIssuesIssueIdIssueImagesSchemaQuery = {
  Response: GetApiProjectsProjectIdIssuesIssueIdIssueImages200Schema;
  PathParams: GetApiProjectsProjectIdIssuesIssueIdIssueImagesPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdIssuesIssueIdIssueImagesQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdIssuesIssueIdIssueImages401Schema
    | GetApiProjectsProjectIdIssuesIssueIdIssueImages403Schema
    | GetApiProjectsProjectIdIssuesIssueIdIssueImages404Schema;
};
