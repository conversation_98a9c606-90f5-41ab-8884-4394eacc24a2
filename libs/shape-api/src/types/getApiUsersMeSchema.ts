/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { UserSchema } from './userSchema';

/**
 * @description Authenticated user
 */
export type GetApiUsersMe200Schema = UserSchema;

/**
 * @description Authentication required
 */
export type GetApiUsersMe401Schema = AuthenticationErrorSchema;

export type GetApiUsersMeQueryResponseSchema = GetApiUsersMe200Schema;

export type GetApiUsersMeSchemaQuery = {
  Response: GetApiUsersMe200Schema;
  Errors: GetApiUsersMe401Schema;
};
