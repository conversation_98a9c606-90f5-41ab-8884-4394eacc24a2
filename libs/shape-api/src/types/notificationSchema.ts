/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { NotificationParametersSchema } from './notificationParametersSchema';

export type NotificationSchema = NotificationParametersSchema & {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string, date-time
   */
  createdAt: string;
  /**
   * @type boolean
   */
  read: boolean;
  /**
   * @type object | undefined
   */
  project?: {
    /**
     * @type string, uuid
     */
    id: string;
    /**
     * @type string
     */
    title: string;
    /**
     * @type string
     */
    shortName: string;
  };
};
