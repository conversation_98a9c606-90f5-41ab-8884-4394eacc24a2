/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationParametersSchema } from './notificationParametersSchema';

export type NotificationSchema = {
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string, date-time
   */
  createdAt: string;
  /**
   * @type boolean
   */
  read: boolean;
  /**
   * @type object | undefined
   */
  project?: {
    /**
     * @type string, uuid
     */
    id: string;
    /**
     * @type string
     */
    title: string;
    /**
     * @type string
     */
    shortName: string;
  };
} & NotificationParametersSchema;
