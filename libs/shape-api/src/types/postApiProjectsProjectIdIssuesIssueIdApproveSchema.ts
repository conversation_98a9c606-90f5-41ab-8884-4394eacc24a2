/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueSchema } from './issueSchema';

export type PostApiProjectsProjectIdIssuesIssueIdApprovePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description Issue updated
 */
export type PostApiProjectsProjectIdIssuesIssueIdApprove200Schema = IssueSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdIssuesIssueIdApprove400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdIssuesIssueIdApprove401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdIssuesIssueIdApprove403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type PostApiProjectsProjectIdIssuesIssueIdApprove404Schema = unknown;

export type PostApiProjectsProjectIdIssuesIssueIdApproveMutationResponseSchema =
  PostApiProjectsProjectIdIssuesIssueIdApprove200Schema;

export type PostApiProjectsProjectIdIssuesIssueIdApproveSchemaMutation = {
  Response: PostApiProjectsProjectIdIssuesIssueIdApprove200Schema;
  PathParams: PostApiProjectsProjectIdIssuesIssueIdApprovePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdIssuesIssueIdApprove400Schema
    | PostApiProjectsProjectIdIssuesIssueIdApprove401Schema
    | PostApiProjectsProjectIdIssuesIssueIdApprove403Schema
    | PostApiProjectsProjectIdIssuesIssueIdApprove404Schema;
};
