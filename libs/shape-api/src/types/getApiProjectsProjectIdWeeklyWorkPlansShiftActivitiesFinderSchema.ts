/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { WeeklyWorkPlanShiftActivitiesFinderSchema } from './weeklyWorkPlanShiftActivitiesFinderSchema';

export type GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Shift activities finder options
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder200Schema =
  WeeklyWorkPlanShiftActivitiesFinderSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder403Schema = unknown;

/**
 * @description Project not found
 */
export type GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder404Schema = unknown;

export type GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderQueryResponseSchema =
  GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder200Schema;

export type GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderSchemaQuery = {
  Response: GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder200Schema;
  PathParams: GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinderPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder401Schema
    | GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder403Schema
    | GetApiProjectsProjectIdWeeklyWorkPlansShiftActivitiesFinder404Schema;
};
