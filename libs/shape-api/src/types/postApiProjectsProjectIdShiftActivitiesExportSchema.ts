/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { OneOrManyIntegerNullableSchema } from './oneOrManyIntegerNullableSchema';
import type { OneOrManyUuidNullableSchema } from './oneOrManyUuidNullableSchema';
import type { OneOrManyUuidSchema } from './oneOrManyUuidSchema';
import type { QueuedTaskSchema } from './queuedTaskSchema';
import type { ShiftActivityStatusSchema } from './shiftActivityStatusSchema';

export type PostApiProjectsProjectIdShiftActivitiesExportPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Request accepted
 */
export type PostApiProjectsProjectIdShiftActivitiesExport202Schema = QueuedTaskSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftActivitiesExport401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftActivitiesExport403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftActivitiesExport404Schema = unknown;

export type PostApiProjectsProjectIdShiftActivitiesExportMutationRequestSchema = {
  id?: OneOrManyUuidSchema;
  /**
   * @type boolean | undefined
   */
  archived?: boolean;
  status?: ShiftActivityStatusSchema[] | ShiftActivityStatusSchema;
  assigned_team_member_id?: OneOrManyIntegerNullableSchema;
  location_id?: OneOrManyUuidNullableSchema;
  /**
   * @type boolean | undefined
   */
  critical?: boolean;
  owner_id?: OneOrManyIntegerNullableSchema;
  /**
   * @type string | undefined, date-time
   */
  planned_start_date_start?: string;
  /**
   * @type string | undefined, date-time
   */
  planned_start_date_end?: string;
  /**
   * @type string | undefined, date-time
   */
  planned_end_date_start?: string;
  /**
   * @type string | undefined, date-time
   */
  planned_end_date_end?: string;
  /**
   * @type string | undefined, date-time
   */
  expected_finish_date_start?: string;
  /**
   * @type string | undefined, date-time
   */
  expected_finish_date_end?: string;
  /**
   * @type string | undefined, date-time
   */
  actual_start_date_start?: string;
  /**
   * @type string | undefined, date-time
   */
  actual_start_date_end?: string;
  /**
   * @type string | undefined, date-time
   */
  actual_end_date_start?: string;
  /**
   * @type string | undefined, date-time
   */
  actual_end_date_end?: string;
  /**
   * @type boolean | undefined
   */
  ready?: boolean;
  /**
   * @type string | undefined
   */
  search?: string;
};

export type PostApiProjectsProjectIdShiftActivitiesExportMutationResponseSchema =
  PostApiProjectsProjectIdShiftActivitiesExport202Schema;

export type PostApiProjectsProjectIdShiftActivitiesExportSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftActivitiesExport202Schema;
  Request: PostApiProjectsProjectIdShiftActivitiesExportMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdShiftActivitiesExportPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftActivitiesExport401Schema
    | PostApiProjectsProjectIdShiftActivitiesExport403Schema
    | PostApiProjectsProjectIdShiftActivitiesExport404Schema;
};
