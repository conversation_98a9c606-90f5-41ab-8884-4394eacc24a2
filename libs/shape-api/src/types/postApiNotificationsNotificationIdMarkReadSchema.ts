/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { NotificationsMarkedAsReadSchema } from './notificationsMarkedAsReadSchema';

export type PostApiNotificationsNotificationIdMarkReadPathParamsSchema = {
  /**
   * @type string, uuid
   */
  notification_id: string;
};

/**
 * @description Notification marked as read
 */
export type PostApiNotificationsNotificationIdMarkRead200Schema = NotificationsMarkedAsReadSchema;

/**
 * @description Authentication required
 */
export type PostApiNotificationsNotificationIdMarkRead401Schema = AuthenticationErrorSchema;

/**
 * @description Notification not found
 */
export type PostApiNotificationsNotificationIdMarkRead404Schema = unknown;

export type PostApiNotificationsNotificationIdMarkReadMutationRequestSchema = {
  /**
   * @description Also mark similar notifications as read. Only issue comment notifications are supported.
   * @type boolean | undefined
   */
  similar?: boolean;
};

export type PostApiNotificationsNotificationIdMarkReadMutationResponseSchema =
  PostApiNotificationsNotificationIdMarkRead200Schema;

export type PostApiNotificationsNotificationIdMarkReadSchemaMutation = {
  Response: PostApiNotificationsNotificationIdMarkRead200Schema;
  Request: PostApiNotificationsNotificationIdMarkReadMutationRequestSchema;
  PathParams: PostApiNotificationsNotificationIdMarkReadPathParamsSchema;
  Errors: PostApiNotificationsNotificationIdMarkRead401Schema | PostApiNotificationsNotificationIdMarkRead404Schema;
};
