/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueDetailsBasicSchema } from './issueDetailsBasicSchema';
import type { IssueDetailsExtraSchema } from './issueDetailsExtraSchema';
import type { IssueDetailsUpdatesCountSchema } from './issueDetailsUpdatesCountSchema';

export type IssueSchema = IssueDetailsBasicSchema & IssueDetailsUpdatesCountSchema & IssueDetailsExtraSchema;
