/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { IssueDetailsBasicSchema } from './issueDetailsBasicSchema';
import type { IssueDetailsExtraSchema } from './issueDetailsExtraSchema';
import type { IssueDetailsUpdatesCountSchema } from './issueDetailsUpdatesCountSchema';

export type IssueSchema = IssueDetailsBasicSchema & IssueDetailsUpdatesCountSchema & IssueDetailsExtraSchema;
