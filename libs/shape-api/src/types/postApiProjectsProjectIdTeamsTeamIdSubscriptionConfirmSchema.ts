/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamSubscriptionPlanSchema } from './teamSubscriptionPlanSchema';

export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirmPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
};

/**
 * @description New subscription plan confirmed
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirm200Schema = TeamSubscriptionPlanSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirm401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirm403Schema = ErrorSchema;

/**
 * @description Team not found
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirm404Schema = unknown;

/**
 * @description Confirmation failed
 */
export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirm422Schema = ErrorSchema;

export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirmMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  subscription_session_id?: string;
};

export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirmMutationResponseSchema =
  PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirm200Schema;

export type PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirmSchemaMutation = {
  Response: PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirm200Schema;
  Request: PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirmMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirmPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirm401Schema
    | PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirm403Schema
    | PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirm404Schema
    | PostApiProjectsProjectIdTeamsTeamIdSubscriptionConfirm422Schema;
};
