/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ShiftActivityOverviewDailyProgressListSchema } from './shiftActivityOverviewDailyProgressListSchema';

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description List of daily progress
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgress200Schema =
  ShiftActivityOverviewDailyProgressListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgress401Schema = AuthenticationErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgress404Schema = unknown;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressQueryResponseSchema =
  GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgress200Schema;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgress200Schema;
  PathParams: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgressPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgress401Schema
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdDailyProgress404Schema;
};
