/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ProjectAccessRequestSchema } from './projectAccessRequestSchema';

export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAcceptPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  project_access_request_id: string;
};

/**
 * @description Project access request accepted
 */
export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAccept200Schema = ProjectAccessRequestSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAccept401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAccept403Schema = ErrorSchema;

/**
 * @description Request not found
 */
export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAccept404Schema = unknown;

/**
 * @description Accept failed
 */
export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAccept422Schema = ErrorSchema;

export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAcceptMutationRequestSchema = {
  /**
   * @type string
   */
  role: string;
  /**
   * @type string
   */
  construction_role: string;
};

export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAcceptMutationResponseSchema =
  PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAccept200Schema;

export type PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAcceptSchemaMutation = {
  Response: PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAccept200Schema;
  Request: PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAcceptMutationRequestSchema;
  PathParams: PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAcceptPathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAccept401Schema
    | PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAccept403Schema
    | PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAccept404Schema
    | PostApiProjectsProjectIdAccessRequestsProjectAccessRequestIdAccept422Schema;
};
