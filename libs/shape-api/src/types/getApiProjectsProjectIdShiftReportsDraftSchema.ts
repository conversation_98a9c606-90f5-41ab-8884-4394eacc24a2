/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftReportListSchema } from './shiftReportListSchema';

export type GetApiProjectsProjectIdShiftReportsDraftPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

export type GetApiProjectsProjectIdShiftReportsDraftQueryParamsSchema = {
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of draft shift reports
 */
export type GetApiProjectsProjectIdShiftReportsDraft200Schema = ShiftReportListSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdShiftReportsDraft400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftReportsDraft401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdShiftReportsDraft403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftReportsDraft404Schema = unknown;

export type GetApiProjectsProjectIdShiftReportsDraftQueryResponseSchema =
  GetApiProjectsProjectIdShiftReportsDraft200Schema;

export type GetApiProjectsProjectIdShiftReportsDraftSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftReportsDraft200Schema;
  PathParams: GetApiProjectsProjectIdShiftReportsDraftPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdShiftReportsDraftQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftReportsDraft400Schema
    | GetApiProjectsProjectIdShiftReportsDraft401Schema
    | GetApiProjectsProjectIdShiftReportsDraft403Schema
    | GetApiProjectsProjectIdShiftReportsDraft404Schema;
};
