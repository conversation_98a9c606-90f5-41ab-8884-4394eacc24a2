/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { SmartIssueDataSchema } from './smartIssueDataSchema';

export const queuedTaskResultSmartIssueOperationEnum = {
  ai_smart_issue: 'ai_smart_issue',
} as const;

export type QueuedTaskResultSmartIssueOperationEnumSchema =
  (typeof queuedTaskResultSmartIssueOperationEnum)[keyof typeof queuedTaskResultSmartIssueOperationEnum];

export type QueuedTaskResultSmartIssueSchema = {
  /**
   * @type string
   */
  operation: QueuedTaskResultSmartIssueOperationEnumSchema;
  result: SmartIssueDataSchema | null;
};
