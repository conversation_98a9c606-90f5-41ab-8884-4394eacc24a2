/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export type PostApiProjectsProjectIdArchivePathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
};

/**
 * @description Project archived
 */
export type PostApiProjectsProjectIdArchive200Schema = unknown;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdArchive401Schema = unknown;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdArchive403Schema = unknown;

/**
 * @description Project not found
 */
export type PostApiProjectsProjectIdArchive404Schema = unknown;

export type PostApiProjectsProjectIdArchiveMutationResponseSchema = PostApiProjectsProjectIdArchive200Schema;

export type PostApiProjectsProjectIdArchiveSchemaMutation = {
  Response: PostApiProjectsProjectIdArchive200Schema;
  PathParams: PostApiProjectsProjectIdArchivePathParamsSchema;
  Errors:
    | PostApiProjectsProjectIdArchive401Schema
    | PostApiProjectsProjectIdArchive403Schema
    | PostApiProjectsProjectIdArchive404Schema;
};
