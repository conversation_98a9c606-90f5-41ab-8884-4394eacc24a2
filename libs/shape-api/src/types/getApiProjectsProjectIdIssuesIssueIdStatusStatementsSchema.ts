/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueStatusStatementListSchema } from './issueStatusStatementListSchema';

export type GetApiProjectsProjectIdIssuesIssueIdStatusStatementsPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

/**
 * @description List of issue status statements
 */
export type GetApiProjectsProjectIdIssuesIssueIdStatusStatements200Schema = IssueStatusStatementListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdIssuesIssueIdStatusStatements401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type GetApiProjectsProjectIdIssuesIssueIdStatusStatements403Schema = ErrorSchema;

/**
 * @description Issue not found
 */
export type GetApiProjectsProjectIdIssuesIssueIdStatusStatements404Schema = unknown;

export type GetApiProjectsProjectIdIssuesIssueIdStatusStatementsQueryResponseSchema =
  GetApiProjectsProjectIdIssuesIssueIdStatusStatements200Schema;

export type GetApiProjectsProjectIdIssuesIssueIdStatusStatementsSchemaQuery = {
  Response: GetApiProjectsProjectIdIssuesIssueIdStatusStatements200Schema;
  PathParams: GetApiProjectsProjectIdIssuesIssueIdStatusStatementsPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdIssuesIssueIdStatusStatements401Schema
    | GetApiProjectsProjectIdIssuesIssueIdStatusStatements403Schema
    | GetApiProjectsProjectIdIssuesIssueIdStatusStatements404Schema;
};
