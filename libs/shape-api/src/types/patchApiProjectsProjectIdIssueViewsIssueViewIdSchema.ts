/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueViewFilterItemSchema } from './issueViewFilterItemSchema';
import type { IssueViewGroupBySchema } from './issueViewGroupBySchema';
import type { IssueViewGroupPropertySchema } from './issueViewGroupPropertySchema';
import type { IssueViewSchema } from './issueViewSchema';

export type PatchApiProjectsProjectIdIssueViewsIssueViewIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_view_id: string;
};

/**
 * @description Issue view updated
 */
export type PatchApiProjectsProjectIdIssueViewsIssueViewId200Schema = IssueViewSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdIssueViewsIssueViewId401Schema = AuthenticationErrorSchema;

/**
 * @description Not found
 */
export type PatchApiProjectsProjectIdIssueViewsIssueViewId404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdIssueViewsIssueViewId422Schema = ErrorSchema;

export const patchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSortByEnum = {
  created: 'created',
  delay_finish: 'delay_finish',
  delay_start: 'delay_start',
  due_date: 'due_date',
  impact: 'impact',
  planned_date: 'planned_date',
  reference: 'reference',
  resolved: 'resolved',
  state: 'state',
  title: 'title',
  updated: 'updated',
} as const;

export type PatchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSortByEnumSchema =
  (typeof patchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSortByEnum)[keyof typeof patchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSortByEnum];

export const patchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSortOrderEnum = {
  asc: 'asc',
  desc: 'desc',
} as const;

export type PatchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSortOrderEnumSchema =
  (typeof patchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSortOrderEnum)[keyof typeof patchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSortOrderEnum];

export type PatchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  name?: string;
  /**
   * @type array
   */
  filter_properties?: IssueViewFilterItemSchema[] | null;
  /**
   * @type array
   */
  group_properties?: IssueViewGroupPropertySchema[] | null;
  group_by?: IssueViewGroupBySchema | null;
  /**
   * @type string
   */
  sort_by?: PatchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSortByEnumSchema | null;
  /**
   * @type string
   */
  sort_order?: PatchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSortOrderEnumSchema | null;
};

export type PatchApiProjectsProjectIdIssueViewsIssueViewIdMutationResponseSchema =
  PatchApiProjectsProjectIdIssueViewsIssueViewId200Schema;

export type PatchApiProjectsProjectIdIssueViewsIssueViewIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdIssueViewsIssueViewId200Schema;
  Request: PatchApiProjectsProjectIdIssueViewsIssueViewIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdIssueViewsIssueViewIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdIssueViewsIssueViewId401Schema
    | PatchApiProjectsProjectIdIssueViewsIssueViewId404Schema
    | PatchApiProjectsProjectIdIssueViewsIssueViewId422Schema;
};
