/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { TeamSchema } from './teamSchema';

export type PatchApiProjectsProjectIdTeamsTeamIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  team_id: string;
};

/**
 * @description Team updated
 */
export type PatchApiProjectsProjectIdTeamsTeamId200Schema = TeamSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdTeamsTeamId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdTeamsTeamId403Schema = ErrorSchema;

/**
 * @description Team not found
 */
export type PatchApiProjectsProjectIdTeamsTeamId404Schema = unknown;

export type PatchApiProjectsProjectIdTeamsTeamIdMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  display_name?: string;
};

export type PatchApiProjectsProjectIdTeamsTeamIdMutationResponseSchema = PatchApiProjectsProjectIdTeamsTeamId200Schema;

export type PatchApiProjectsProjectIdTeamsTeamIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdTeamsTeamId200Schema;
  Request: PatchApiProjectsProjectIdTeamsTeamIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdTeamsTeamIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdTeamsTeamId401Schema
    | PatchApiProjectsProjectIdTeamsTeamId403Schema
    | PatchApiProjectsProjectIdTeamsTeamId404Schema;
};
