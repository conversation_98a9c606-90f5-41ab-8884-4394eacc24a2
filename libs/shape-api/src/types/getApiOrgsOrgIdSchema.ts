/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { OrgSchema } from './orgSchema';

export type GetApiOrgsOrgIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  org_id: string;
};

/**
 * @description Org found
 */
export type GetApiOrgsOrgId200Schema = OrgSchema;

/**
 * @description Authentication required
 */
export type GetApiOrgsOrgId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorized
 */
export type GetApiOrgsOrgId403Schema = ErrorSchema;

/**
 * @description Org not found
 */
export type GetApiOrgsOrgId404Schema = unknown;

export type GetApiOrgsOrgIdQueryResponseSchema = GetApiOrgsOrgId200Schema;

export type GetApiOrgsOrgIdSchemaQuery = {
  Response: GetApiOrgsOrgId200Schema;
  PathParams: GetApiOrgsOrgIdPathParamsSchema;
  Errors: GetApiOrgsOrgId401Schema | GetApiOrgsOrgId403Schema | GetApiOrgsOrgId404Schema;
};
