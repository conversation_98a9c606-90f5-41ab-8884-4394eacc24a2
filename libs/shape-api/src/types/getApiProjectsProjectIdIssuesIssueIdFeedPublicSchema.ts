/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { IssueFeedSchema } from './issueFeedSchema';

export type GetApiProjectsProjectIdIssuesIssueIdFeedPublicPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  issue_id: string;
};

export type GetApiProjectsProjectIdIssuesIssueIdFeedPublicQueryParamsSchema = {
  /**
   * @description Number of records per page
   * @type integer | undefined
   */
  page_size?: number;
  /**
   * @description Cursor to get records after it
   * @type string | undefined
   */
  after?: string;
  /**
   * @description Cursor to get records before it
   * @type string | undefined
   */
  before?: string;
};

/**
 * @description List of public events
 */
export type GetApiProjectsProjectIdIssuesIssueIdFeedPublic200Schema = IssueFeedSchema;

/**
 * @description Bad request
 */
export type GetApiProjectsProjectIdIssuesIssueIdFeedPublic400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdIssuesIssueIdFeedPublic401Schema = AuthenticationErrorSchema;

/**
 * @description Issue not found
 */
export type GetApiProjectsProjectIdIssuesIssueIdFeedPublic404Schema = unknown;

export type GetApiProjectsProjectIdIssuesIssueIdFeedPublicQueryResponseSchema =
  GetApiProjectsProjectIdIssuesIssueIdFeedPublic200Schema;

export type GetApiProjectsProjectIdIssuesIssueIdFeedPublicSchemaQuery = {
  Response: GetApiProjectsProjectIdIssuesIssueIdFeedPublic200Schema;
  PathParams: GetApiProjectsProjectIdIssuesIssueIdFeedPublicPathParamsSchema;
  QueryParams: GetApiProjectsProjectIdIssuesIssueIdFeedPublicQueryParamsSchema;
  Errors:
    | GetApiProjectsProjectIdIssuesIssueIdFeedPublic400Schema
    | GetApiProjectsProjectIdIssuesIssueIdFeedPublic401Schema
    | GetApiProjectsProjectIdIssuesIssueIdFeedPublic404Schema;
};
