/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { LocationSchema } from './locationSchema';

export type PatchApiProjectsProjectIdLocationsLocationIdPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  location_id: string;
};

/**
 * @description Location updated
 */
export type PatchApiProjectsProjectIdLocationsLocationId200Schema = LocationSchema;

/**
 * @description Authentication required
 */
export type PatchApiProjectsProjectIdLocationsLocationId401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PatchApiProjectsProjectIdLocationsLocationId403Schema = ErrorSchema;

/**
 * @description Location not found
 */
export type PatchApiProjectsProjectIdLocationsLocationId404Schema = unknown;

/**
 * @description Update failed
 */
export type PatchApiProjectsProjectIdLocationsLocationId422Schema = ErrorSchema;

export type PatchApiProjectsProjectIdLocationsLocationIdMutationRequestSchema = {
  /**
   * @type string | undefined
   */
  name?: string;
  /**
   * @type string | undefined
   */
  short_code?: string;
  /**
   * @type string | undefined, uuid
   */
  parent_location_id?: string;
  /**
   * @type integer | undefined
   */
  sort_position?: number;
};

export type PatchApiProjectsProjectIdLocationsLocationIdMutationResponseSchema =
  PatchApiProjectsProjectIdLocationsLocationId200Schema;

export type PatchApiProjectsProjectIdLocationsLocationIdSchemaMutation = {
  Response: PatchApiProjectsProjectIdLocationsLocationId200Schema;
  Request: PatchApiProjectsProjectIdLocationsLocationIdMutationRequestSchema;
  PathParams: PatchApiProjectsProjectIdLocationsLocationIdPathParamsSchema;
  Errors:
    | PatchApiProjectsProjectIdLocationsLocationId401Schema
    | PatchApiProjectsProjectIdLocationsLocationId403Schema
    | PatchApiProjectsProjectIdLocationsLocationId404Schema
    | PatchApiProjectsProjectIdLocationsLocationId422Schema;
};
