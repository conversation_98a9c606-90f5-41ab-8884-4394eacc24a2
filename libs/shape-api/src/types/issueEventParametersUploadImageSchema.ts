/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { IssueEventParametersUploadImageArrayItemSchema } from './issueEventParametersUploadImageArrayItemSchema';

export const issueEventParametersUploadImageEventTypeEnum = {
  upload_image: 'upload_image',
} as const;

export type IssueEventParametersUploadImageEventTypeEnumSchema =
  (typeof issueEventParametersUploadImageEventTypeEnum)[keyof typeof issueEventParametersUploadImageEventTypeEnum];

export type IssueEventParametersUploadImageSchema = {
  /**
   * @type string
   */
  eventType: IssueEventParametersUploadImageEventTypeEnumSchema;
  /**
   * @type object
   */
  parameters: {
    /**
     * @type array | undefined
     */
    issueImageIds?: string[];
    /**
     * @type array | undefined
     */
    imageUrls?: string[];
  };
  /**
   * @type object
   */
  resources: {
    /**
     * @type array
     */
    images: IssueEventParametersUploadImageArrayItemSchema[];
  };
};
