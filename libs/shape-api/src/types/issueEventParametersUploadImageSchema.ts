/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueEventParametersUploadImageArrayItemSchema } from './issueEventParametersUploadImageArrayItemSchema';

export type IssueEventParametersUploadImageSchema = {
  /**
   * @type string
   */
  eventType: string;
  /**
   * @type object
   */
  parameters: {
    /**
     * @type array | undefined
     */
    issueImageIds?: string[];
    /**
     * @type array | undefined
     */
    imageUrls?: string[];
  };
  /**
   * @type object
   */
  resources: {
    /**
     * @type array
     */
    images: IssueEventParametersUploadImageArrayItemSchema[];
  };
};
