/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const weeklyWorkPlanActivityVarianceCategoriesEnum = {
  approval_delays: 'approval_delays',
  client_changes: 'client_changes',
  delay_in_decision_or_communication: 'delay_in_decision_or_communication',
  design_information_issues: 'design_information_issues',
  documentation_issues: 'documentation_issues',
  equipment_unavailability: 'equipment_unavailability',
  inaccurate_estimates: 'inaccurate_estimates',
  inadequate_access_to_workface: 'inadequate_access_to_workface',
  material_shortage: 'material_shortage',
  miscellaneous: 'miscellaneous',
  personnel_shortage: 'personnel_shortage',
  previous_trade_handover_delay: 'previous_trade_handover_delay',
  priority_reassessment: 'priority_reassessment',
  process_inefficiencies: 'process_inefficiencies',
  rework_required: 'rework_required',
  safety_concerns_unsafe_conditions: 'safety_concerns_unsafe_conditions',
  scheduling_conflicts: 'scheduling_conflicts',
  technical_challenges: 'technical_challenges',
  weather_conditions: 'weather_conditions',
} as const;

export type WeeklyWorkPlanActivityVarianceCategoriesEnumSchema =
  (typeof weeklyWorkPlanActivityVarianceCategoriesEnum)[keyof typeof weeklyWorkPlanActivityVarianceCategoriesEnum];

export type WeeklyWorkPlanActivityVarianceCategoriesSchema = WeeklyWorkPlanActivityVarianceCategoriesEnumSchema;
