/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

export const issueViewGroupByEnum = {
  category: 'category',
  custom_field: 'custom_field',
  discipline: 'discipline',
  impact: 'impact',
  location: 'location',
  observer: 'observer',
  observer_team: 'observer_team',
  responsible: 'responsible',
  responsible_team: 'responsible_team',
  state: 'state',
  sub_category: 'sub_category',
  team_issues: 'team_issues',
  user_issues: 'user_issues',
} as const;

export type IssueViewGroupByEnumSchema = (typeof issueViewGroupByEnum)[keyof typeof issueViewGroupByEnum];

export const issueViewGroupByEnum2 = {
  category: 'category',
  custom_field: 'custom_field',
  discipline: 'discipline',
  impact: 'impact',
  location: 'location',
  observer: 'observer',
  observer_team: 'observer_team',
  responsible: 'responsible',
  responsible_team: 'responsible_team',
  state: 'state',
  sub_category: 'sub_category',
  team_issues: 'team_issues',
  user_issues: 'user_issues',
} as const;

export type IssueViewGroupByEnum2Schema = (typeof issueViewGroupByEnum2)[keyof typeof issueViewGroupByEnum2];

export type IssueViewGroupBySchema = (IssueViewGroupByEnumSchema | IssueViewGroupByEnum2Schema[]) | null;
