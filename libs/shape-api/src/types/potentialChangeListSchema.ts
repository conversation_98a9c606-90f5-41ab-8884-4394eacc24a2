/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { CursorPaginationMetaSchema } from './cursorPaginationMetaSchema';
import type { PotentialChangeDetailsBasicSchema } from './potentialChangeDetailsBasicSchema';

export type PotentialChangeListSchema = {
  /**
   * @type array
   */
  entries: PotentialChangeDetailsBasicSchema[];
  /**
   * @type object
   */
  meta: CursorPaginationMetaSchema;
};
