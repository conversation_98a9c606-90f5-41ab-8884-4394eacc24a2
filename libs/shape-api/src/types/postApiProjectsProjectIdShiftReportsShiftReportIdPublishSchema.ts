/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ErrorSchema } from './errorSchema';
import type { ShiftReportPublishSchema } from './shiftReportPublishSchema';

export type PostApiProjectsProjectIdShiftReportsShiftReportIdPublishPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_report_id: string;
};

export const postApiProjectsProjectIdShiftReportsShiftReportIdPublishQueryParamsFileTypeEnum = {
  pdf: 'pdf',
  csv: 'csv',
  xlsx: 'xlsx',
} as const;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdPublishQueryParamsFileTypeEnumSchema =
  (typeof postApiProjectsProjectIdShiftReportsShiftReportIdPublishQueryParamsFileTypeEnum)[keyof typeof postApiProjectsProjectIdShiftReportsShiftReportIdPublishQueryParamsFileTypeEnum];

export type PostApiProjectsProjectIdShiftReportsShiftReportIdPublishQueryParamsSchema = {
  /**
   * @type boolean | undefined
   */
  export?: boolean;
  /**
   * @type string | undefined
   */
  file_type?: PostApiProjectsProjectIdShiftReportsShiftReportIdPublishQueryParamsFileTypeEnumSchema;
};

/**
 * @description Shift report published
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdPublish200Schema = ShiftReportPublishSchema;

/**
 * @description Bad request
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdPublish400Schema = ErrorSchema;

/**
 * @description Authentication required
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdPublish401Schema = AuthenticationErrorSchema;

/**
 * @description Not authorised
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdPublish403Schema = ErrorSchema;

/**
 * @description Not found
 */
export type PostApiProjectsProjectIdShiftReportsShiftReportIdPublish404Schema = unknown;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdPublishMutationResponseSchema =
  PostApiProjectsProjectIdShiftReportsShiftReportIdPublish200Schema;

export type PostApiProjectsProjectIdShiftReportsShiftReportIdPublishSchemaMutation = {
  Response: PostApiProjectsProjectIdShiftReportsShiftReportIdPublish200Schema;
  PathParams: PostApiProjectsProjectIdShiftReportsShiftReportIdPublishPathParamsSchema;
  QueryParams: PostApiProjectsProjectIdShiftReportsShiftReportIdPublishQueryParamsSchema;
  Errors:
    | PostApiProjectsProjectIdShiftReportsShiftReportIdPublish400Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdPublish401Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdPublish403Schema
    | PostApiProjectsProjectIdShiftReportsShiftReportIdPublish404Schema;
};
