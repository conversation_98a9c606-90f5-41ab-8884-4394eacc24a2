/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AgreementSchema } from './agreementSchema';

/**
 * @description Latest End User Agreement
 */
export type GetApiAgreementsLatestEua200Schema = AgreementSchema;

/**
 * @description No Agreements in the database
 */
export type GetApiAgreementsLatestEua404Schema = unknown;

export type GetApiAgreementsLatestEuaQueryResponseSchema = GetApiAgreementsLatestEua200Schema;

export type GetApiAgreementsLatestEuaSchemaQuery = {
  Response: GetApiAgreementsLatestEua200Schema;
  Errors: GetApiAgreementsLatestEua404Schema;
};
