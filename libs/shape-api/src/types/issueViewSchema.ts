/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { IssueViewFilterItemSchema } from './issueViewFilterItemSchema';
import type { IssueViewGroupBySchema } from './issueViewGroupBySchema';
import type { IssueViewGroupPropertySchema } from './issueViewGroupPropertySchema';

export const issueViewSortByEnum = {
  created: 'created',
  delay_finish: 'delay_finish',
  delay_start: 'delay_start',
  due_date: 'due_date',
  impact: 'impact',
  planned_date: 'planned_date',
  reference: 'reference',
  resolved: 'resolved',
  state: 'state',
  title: 'title',
  updated: 'updated',
} as const;

export type IssueViewSortByEnumSchema = (typeof issueViewSortByEnum)[keyof typeof issueViewSortByEnum];

export const issueViewSortOrderEnum = {
  asc: 'asc',
  desc: 'desc',
} as const;

export type IssueViewSortOrderEnumSchema = (typeof issueViewSortOrderEnum)[keyof typeof issueViewSortOrderEnum];

export type IssueViewSchema = {
  /**
   * @type array
   */
  filterProperties: IssueViewFilterItemSchema[];
  groupBy: IssueViewGroupBySchema | null;
  /**
   * @type array
   */
  groupProperties: IssueViewGroupPropertySchema[];
  /**
   * @type string, uuid
   */
  id: string;
  /**
   * @type string
   */
  name: string;
  /**
   * @type string
   */
  sortBy: IssueViewSortByEnumSchema | null;
  /**
   * @type string
   */
  sortOrder: IssueViewSortOrderEnumSchema | null;
};
