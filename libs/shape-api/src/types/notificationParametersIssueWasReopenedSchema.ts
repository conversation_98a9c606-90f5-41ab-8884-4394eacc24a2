/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersIssueWasReopenedTypeEnum = {
  issue_was_reopened: 'issue_was_reopened',
} as const;

export type NotificationParametersIssueWasReopenedTypeEnumSchema =
  (typeof notificationParametersIssueWasReopenedTypeEnum)[keyof typeof notificationParametersIssueWasReopenedTypeEnum];

export type NotificationParametersIssueWasReopenedSchema = {
  /**
   * @type string
   */
  type: NotificationParametersIssueWasReopenedTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type string, uuid
     */
    projectId: string;
  };
};
