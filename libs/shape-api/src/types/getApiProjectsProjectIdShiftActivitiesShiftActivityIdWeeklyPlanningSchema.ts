/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ShiftActivityOverviewWeeklyPlanningListSchema } from './shiftActivityOverviewWeeklyPlanningListSchema';

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanningPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description List of weekly planning items of an activity
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanning200Schema =
  ShiftActivityOverviewWeeklyPlanningListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanning401Schema = AuthenticationErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanning404Schema = unknown;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanningQueryResponseSchema =
  GetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanning200Schema;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanningSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanning200Schema;
  PathParams: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanningPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanning401Schema
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdWeeklyPlanning404Schema;
};
