/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export type NotificationParametersNewIssuePrivateCommentSchema = {
  /**
   * @type string
   */
  type: string;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    commentId: string;
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type string
     */
    message: string;
    /**
     * @type string, uuid
     */
    projectId: string;
  };
};
