/**
 * Generated by kubb.
 * Do not edit manually.
 * Shape API
 */

import type { NotificationActorSchema } from './notificationActorSchema';

export const notificationParametersNewIssuePrivateCommentTypeEnum = {
  new_issue_private_comment: 'new_issue_private_comment',
} as const;

export type NotificationParametersNewIssuePrivateCommentTypeEnumSchema =
  (typeof notificationParametersNewIssuePrivateCommentTypeEnum)[keyof typeof notificationParametersNewIssuePrivateCommentTypeEnum];

export type NotificationParametersNewIssuePrivateCommentSchema = {
  /**
   * @type string
   */
  type: NotificationParametersNewIssuePrivateCommentTypeEnumSchema;
  /**
   * @type object
   */
  actor: NotificationActorSchema;
  /**
   * @type object
   */
  params: {
    /**
     * @type string, uuid
     */
    commentId: string;
    /**
     * @type string, uuid
     */
    issueId: string;
    /**
     * @type string
     */
    issueTitle: string;
    /**
     * @type string
     */
    message: string;
    /**
     * @type string, uuid
     */
    projectId: string;
  };
};
