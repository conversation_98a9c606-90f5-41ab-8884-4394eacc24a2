/**
 * Generated by <PERSON><PERSON> (https://kubb.dev/).
 * Do not edit manually.
 */

import type { AuthenticationErrorSchema } from './authenticationErrorSchema';
import type { ShiftActivityBlockerListSchema } from './shiftActivityBlockerListSchema';

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersPathParamsSchema = {
  /**
   * @type string, uuid
   */
  project_id: string;
  /**
   * @type string, uuid
   */
  shift_activity_id: string;
};

/**
 * @description List of shift activity blockers
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockers200Schema = ShiftActivityBlockerListSchema;

/**
 * @description Authentication required
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockers401Schema = AuthenticationErrorSchema;

/**
 * @description Not found
 */
export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockers404Schema = unknown;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersQueryResponseSchema =
  GetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockers200Schema;

export type GetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersSchemaQuery = {
  Response: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockers200Schema;
  PathParams: GetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockersPathParamsSchema;
  Errors:
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockers401Schema
    | GetApiProjectsProjectIdShiftActivitiesShiftActivityIdBlockers404Schema;
};
