import { useMessageGetter } from '@messageformat/react';
import type { ChangeSignalSchema } from '@shape-construction/api/src';
import { Button, CollapsableCard, Divider } from '@shape-construction/arch-ui';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import FileThumbnail from '@shape-construction/arch-ui/src/FileThumbnail/FileThumbnail';
import { IssueTrackerIcon, ShiftReportsIcon } from '@shape-construction/arch-ui/src/Icons/product-logo';
import { ChevronDownIcon, ChevronUpIcon, UnlinkIcon } from '@shape-construction/arch-ui/src/Icons/solid';
import { formatDate, formatDateAndTime } from '@shape-construction/utils/DateTime';
import { useQuery } from '@tanstack/react-query';
import { issueStateInformation } from 'app/components/IssueStateInformation/IssueStateInformation';
import { getDisciplinePath } from 'app/components/Utils/disciplines';
import { truncatedLocationPath } from 'app/components/Utils/locations';
import { getImpactRecord } from 'app/constants/Impact';
import { useCurrentProject } from 'app/contexts/currentProject';
import { getProjectDisciplinesQueryOptions } from 'app/queries/projects/disciplines';
import { useProjectLocations } from 'app/queries/projects/locations';
import { useProjectPeople } from 'app/queries/projects/people';
import { useShiftReport } from 'app/queries/shiftReports/shiftReports';
import { Link } from 'react-router';
import { getSignalLink } from '../../change-signals/components/ChangeSignalsTable';

function renderSignalTypeWithIcon(
  signalType: ChangeSignalSchema['signalType'],
  issueTitle: ChangeSignalSchema['title']
) {
  const messages = useMessageGetter('controlCenter.commercialTracker');
  if (signalType === 'issue') {
    return (
      <div className="flex items-center gap-1 text-xs leading-4 font-normal text-neutral-subtle">
        <IssueTrackerIcon height={12} width={12} />
        <span>{messages('modals.potentialChangeLinkSignalsDrawer.changeSignalsList.issue')}</span>
        <span className="uppercase font-medium">{issueTitle}</span>
      </div>
    );
  }
  return (
    <div className="flex items-center gap-1 text-xs leading-4 font-normal text-neutral-subtle">
      <ShiftReportsIcon height={12} width={12} />
      <span>{messages('modals.potentialChangeLinkSignalsDrawer.changeSignalsList.downtime')}</span>
    </div>
  );
}

const isDowntimeSignal = (signal: ChangeSignalSchema) => {
  return signal.signalType === 'downtime';
};

type PotentialChangeSignalItemProps = {
  readonly signal: ChangeSignalSchema;
  onUnlinkChangeSignalClick: (
    signalId: ChangeSignalSchema['signalId'],
    signalType: ChangeSignalSchema['signalType']
  ) => void;
  isUnlinkingChangeSignals?: boolean;
  canUnlink: boolean;
};

export function PotentialChangeSignalItem({
  signal,
  onUnlinkChangeSignalClick,
  isUnlinkingChangeSignals,
  canUnlink,
}: PotentialChangeSignalItemProps) {
  const project = useCurrentProject();
  const messages = useMessageGetter('controlCenter.commercialTracker');
  const issueMessages = useMessageGetter('issue.detail');

  const impactRecord = getImpactRecord(issueMessages);
  const shiftReportId = isDowntimeSignal(signal) ? signal.shiftReport?.id : '';
  const { data: shiftReport } = useShiftReport(project.id, shiftReportId, {
    query: { enabled: signal.signalType === 'downtime' },
  });

  const { data: projectPeople } = useProjectPeople(project.id);
  const { data: locations } = useProjectLocations(project.id);
  const { data: disciplines } = useQuery(getProjectDisciplinesQueryOptions(project.id));

  const user = projectPeople?.find((person) => person.id === signal.teamMemberId)?.user;
  const disciplineForIssue =
    signal.signalType === 'issue' ? getDisciplinePath(disciplines, signal.issue.disciplineId) : [];

  return (
    <CollapsableCard className="rounded-sm border-neutral-subtle border-1 bg-neutral-subtlest">
      {({ isOpen }) => (
        <>
          <CollapsableCard.Header
            isOpen={isOpen}
            openIcon={ChevronDownIcon}
            closeIcon={ChevronUpIcon}
            className="flex gap-2 items-center"
          >
            {renderSignalTypeWithIcon(signal.signalType, signal.title)}
          </CollapsableCard.Header>
          <CollapsableCard.Actions>
            {canUnlink && (
              <Button
                aria-label={messages('modals.potentialChangeLinkSignalsDrawer.unlink')}
                onClick={() => onUnlinkChangeSignalClick(signal.signalId, signal.signalType)}
                size="xs"
                color="danger"
                variant="text"
                leadingIcon={UnlinkIcon}
                disabled={isUnlinkingChangeSignals}
              >
                {messages('modals.potentialChangeLinkSignalsDrawer.unlink')}
              </Button>
            )}
          </CollapsableCard.Actions>
          <CollapsableCard.Subheader>
            <div className="pl-10">
              <Link
                to={getSignalLink(signal, project.id)}
                className="text-link-brand text-sm leading-5 font-normal underline cursor-pointer"
              >
                {signal.title}
              </Link>
            </div>
          </CollapsableCard.Subheader>
          <CollapsableCard.Content>
            <Divider orientation={'horizontal'} />
            <div className="pt-4 flex flex-col gap-4 text-xs leading-4 font-normal">
              <div className="flex gap-4 sm:gap-12 grow flex-wrap">
                <div className="flex flex-col gap-1">
                  <span className="text-neutral-subtle">{messages('fields.author')}</span>
                  <div className="flex gap-1">
                    <UserAvatar size={'xs'} user={user} highlightedColor="primary" />
                    <span className="text-neutral">{user?.name}</span>
                  </div>
                </div>
                <div className="flex flex-col gap-1">
                  <span className="text-neutral-subtle">{messages('fields.createdAt')}</span>
                  <div className="flex gap-1">
                    <span className="text-neutral">
                      {formatDate(signal.publishedAt, project.timezone, 'DD-MMM-YYYY')}
                    </span>
                  </div>
                </div>
                <div className="flex flex-col gap-1">
                  <span className="text-neutral-subtle">
                    {signal.signalType === 'issue' ? messages('fields.impact') : messages('fields.shiftType')}
                  </span>
                  <div className="flex gap-1">
                    <span className="text-neutral">
                      {signal.signalType === 'issue' && signal?.impact
                        ? impactRecord[signal.impact]?.label
                        : shiftReport?.shiftType}
                    </span>
                  </div>
                </div>
                <div className="flex flex-col gap-1">
                  <span className="text-neutral-subtle">
                    {signal.signalType === 'issue' ? messages('fields.status') : messages('fields.timeLostInHrs')}
                  </span>
                  <div className="flex gap-1">
                    <span className="text-neutral">
                      {signal.signalType === 'issue'
                        ? issueStateInformation(signal.issue).displayedState
                        : signal.downtime.timeLost}
                    </span>
                  </div>
                </div>
                {signal.signalType === 'downtime' && (
                  <div className="flex flex-col gap-1">
                    <span className="text-neutral-subtle">{messages('fields.reason')}</span>
                    <div className="flex gap-1 max-w-32">
                      <span className="text-neutral truncate" title={signal.downtime?.causalType ?? ''}>
                        {signal.downtime.causalType}
                      </span>
                    </div>
                  </div>
                )}
              </div>
              {signal.signalType === 'downtime' && (
                <div className="flex flex-col gap-1">
                  <span className="text-neutral-subtle">{messages('fields.linkedIssue')}</span>
                  {signal.downtime.issueId ? (
                    <Link
                      to={`/projects/${project.id}/issues/lists/all?issueId=${signal.downtime.issueId}`}
                      className="text-link-brand text-sm leading-5 font-normal underline cursor-pointer"
                    >
                      {signal.downtime.issueDescription}
                    </Link>
                  ) : (
                    <span className="text-neutral">{'--'}</span>
                  )}
                </div>
              )}
              <div className="flex flex-col gap-1">
                <span className="text-neutral-subtle">{messages('fields.description')}</span>
                <p className="text-neutral">
                  {signal.signalType === 'issue' ? signal.issue.description : signal.downtime.issueDescription}
                </p>
              </div>

              {signal.signalType === 'issue' && (
                <>
                  <div className="flex flex-col gap-1">
                    <span className="text-neutral-subtle">{messages('fields.discipline')}</span>
                    <p className="text-neutral">
                      {disciplineForIssue?.map((discipline) => discipline.name).join(' > ')}
                    </p>
                  </div>
                  <div className="flex flex-col gap-1">
                    <span className="text-neutral-subtle">{messages('fields.location')}</span>
                    <p className="text-neutral">{truncatedLocationPath(locations, signal.locationId)}</p>
                  </div>

                  <div className="flex gap-4 sm:gap-12 grow flex-wrap">
                    <div className="flex flex-col gap-1">
                      <span className="text-neutral-subtle">{messages('fields.observedAt')}</span>
                      <div className="flex gap-1">
                        <span className="text-neutral">
                          {signal.issue.observedAt
                            ? formatDateAndTime(signal.issue.observedAt, project.timezone)
                            : '--'}
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-col gap-1">
                      <span className="text-neutral-subtle">{messages('fields.dueDate')}</span>
                      <div className="flex gap-1">
                        <span className="text-neutral">
                          {signal.issue.dueDate ? formatDateAndTime(signal.issue.dueDate, project.timezone) : '--'}
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-col gap-1">
                      <span className="text-neutral-subtle">{messages('fields.plannedClosureDate')}</span>
                      <div className="flex gap-1">
                        <span className="text-neutral">
                          {signal.issue.plannedClosureDate
                            ? formatDateAndTime(signal.issue.plannedClosureDate, project.timezone)
                            : '--'}
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-col gap-1">
                      <span className="text-neutral-subtle">{messages('fields.closedAt')}</span>
                      <div className="flex gap-1">
                        <span className="text-neutral">
                          {signal.issue.closedAt ? formatDateAndTime(signal.issue.closedAt, project.timezone) : '--'}
                        </span>
                      </div>
                    </div>
                  </div>

                  <div className="flex gap-4 sm:gap-12 grow flex-wrap">
                    <div className="flex flex-col gap-1">
                      <span className="text-neutral-subtle">{messages('fields.delayStart')}</span>
                      <div className="flex gap-1">
                        <span className="text-neutral">
                          {signal.issue.delayStart
                            ? formatDateAndTime(signal.issue.delayStart, project.timezone)
                            : '--'}
                        </span>
                      </div>
                    </div>
                    <div className="flex flex-col gap-1">
                      <span className="text-neutral-subtle">{messages('fields.delayFinish')}</span>
                      <div className="flex gap-1">
                        <span className="text-neutral">
                          {signal.issue.delayFinish
                            ? formatDateAndTime(signal.issue.delayFinish, project.timezone)
                            : '--'}
                        </span>
                      </div>
                    </div>
                  </div>
                </>
              )}

              <div className="flex gap-2 flex-wrap">
                {signal.documents.map((document) => (
                  <FileThumbnail
                    key={document.id}
                    className="w-32"
                    caption={document.filename}
                    extension={document.extension}
                    fileId="5"
                    size="small"
                    thumbnailUrl={document.imageUrl?.s}
                    hideCaption
                  />
                ))}
              </div>
            </div>
          </CollapsableCard.Content>
        </>
      )}
    </CollapsableCard>
  );
}
