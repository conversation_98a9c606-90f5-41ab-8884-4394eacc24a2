import { useMessageGetter } from '@messageformat/react';
import type { PotentialChangeDetailsBasicSchema } from '@shape-construction/api/src';
import { UserAvatar } from '@shape-construction/arch-ui/src/Avatar';
import { useCurrentProject } from 'app/contexts/currentProject';
import { useProjectPerson } from 'app/queries/projects/people';
import React from 'react';
type AuthorProps = {
  record: PotentialChangeDetailsBasicSchema;
};

export const Author: React.FC<AuthorProps> = ({ record }) => {
  const messages = useMessageGetter('controlCenter.commercialTracker.fields');
  const project = useCurrentProject();
  const { data: author } = useProjectPerson(project.id, record.teamMemberId);
  return (
    <div className="flex gap-2 items-center" aria-label={messages('author')}>
      <UserAvatar user={author?.user} size={'xs'} />
      <span className="text-xs leading-4 font-normal text-neutral-subtle">{author?.user.name}</span>
    </div>
  );
};
