import type { PotentialChangeSchema } from '@shape-construction/api/src';
import { useEffect, useState } from 'react';

interface UseRowHighlightOptions {
  /** Duration in milliseconds for how long the highlight should last */
  duration?: number;

  /** Delay in milliseconds before the fade-in starts */
  fadeInDelay?: number;
}

export const useRowHighlight = (
  targetId: PotentialChangeSchema['id'] | undefined,
  currentId: PotentialChangeSchema['id'],
  options: UseRowHighlightOptions = {}
): { isHighlighted: boolean } => {
  const { duration = 5000, fadeInDelay = 10 } = options;
  const [isHighlighted, setIsHighlighted] = useState(false);

  useEffect(() => {
    if (targetId === currentId) {
      setIsHighlighted(false);

      const fadeInTimeout = setTimeout(() => {
        setIsHighlighted(true);
      }, fadeInDelay);

      const fadeOutTimeout = setTimeout(() => {
        setIsHighlighted(false);
      }, duration);

      return () => {
        clearTimeout(fadeInTimeout);
        clearTimeout(fadeOutTimeout);
      };
    }
  }, [targetId, currentId, duration, fadeInDelay]);

  return { isHighlighted };
};
