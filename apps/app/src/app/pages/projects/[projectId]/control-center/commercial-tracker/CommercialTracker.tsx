import type {
  PotentialChangeDetailsBasicSchema,
  PotentialChangeSchema,
  ProjectSchema,
} from '@shape-construction/api/src';
import { useModal } from '@shape-construction/hooks';
import { useSuspenseInfiniteQuery } from '@tanstack/react-query';
import { getPotentialChangesInfiniteQueryOptions } from 'app/queries/control-center/commercial-tracker';
import React from 'react';
import { ChangeSignalsSummary } from './components/ChangeSignalsSummary';
import { PotentialChangeSignalsDrawer } from './components/PotentialChangeSignalsDrawer';
import { PotentialChangesEmptyState } from './components/PotentialChangesEmptyState';
import { PotentialChangesTable } from './components/PotentialChangesTable';

type CommercialTrackerProps = {
  project: ProjectSchema;
  onOpenDrawer: () => void;
  isSelectionMode?: boolean;
  selectedPotentialChangeIds: Array<PotentialChangeSchema['id']> | undefined;
  setSelectedPotentialChangeIds: React.Dispatch<React.SetStateAction<Array<PotentialChangeSchema['id']> | undefined>>;
  titleRef: React.RefObject<HTMLInputElement>;
  onCreatePotentialChange: () => void;
  newRecordId?: PotentialChangeSchema['id'];
};

export const CommercialTracker: React.FC<CommercialTrackerProps> = ({
  project,
  onOpenDrawer,
  isSelectionMode,
  selectedPotentialChangeIds,
  setSelectedPotentialChangeIds,
  titleRef,
  onCreatePotentialChange,
  newRecordId,
}) => {
  const [selectedPotentialChangeForSignals, setSelectedPotentialChangeForSignals] =
    React.useState<PotentialChangeDetailsBasicSchema>();

  const {
    data: potentialChanges,
    fetchNextPage,
    hasNextPage,
    isFetchingNextPage,
  } = useSuspenseInfiniteQuery(getPotentialChangesInfiniteQueryOptions(project.id));

  const {
    open: isPotentialChangeSignalsDrawerOpen,
    openModal: openPotentialChangeSignalsDrawer,
    closeModal: closePotentialChangeSignalsDrawer,
  } = useModal(false);

  const onViewChangeSignals = (potentialChange: PotentialChangeDetailsBasicSchema) => {
    setSelectedPotentialChangeForSignals(potentialChange);
    openPotentialChangeSignalsDrawer();
  };

  return (
    <div className="flex flex-col h-full">
      <ChangeSignalsSummary onOpenDrawer={onOpenDrawer} isSelectionMode={isSelectionMode} />
      {potentialChanges.length > 0 ? (
        <>
          <PotentialChangesTable
            potentialChanges={potentialChanges}
            isSelectionMode={isSelectionMode}
            selectedPotentialChangeIds={selectedPotentialChangeIds}
            setSelectedPotentialChangeIds={setSelectedPotentialChangeIds}
            onViewChangeSignals={onViewChangeSignals}
            titleRef={titleRef}
            hasNextPage={hasNextPage}
            fetchNextPage={fetchNextPage}
            isFetchingNextPage={isFetchingNextPage}
            newRecordId={newRecordId}
            selectedPotentialChangeForSignals={selectedPotentialChangeForSignals}
          />
          {selectedPotentialChangeForSignals && (
            <PotentialChangeSignalsDrawer
              selectedPotentialChange={selectedPotentialChangeForSignals}
              setSelectedPotentialChange={setSelectedPotentialChangeForSignals}
              isOpen={isPotentialChangeSignalsDrawerOpen}
              onClose={closePotentialChangeSignalsDrawer}
            />
          )}
        </>
      ) : (
        <PotentialChangesEmptyState onBrowseChangeSignals={onOpenDrawer} onCreateNewChange={onCreatePotentialChange} />
      )}
    </div>
  );
};
